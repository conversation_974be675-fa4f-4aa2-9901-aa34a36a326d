# 05 - Custom Policies with YAML

## 🎯 Learning Objectives
- Create custom Checkov policies using YAML syntax
- Understand YAML policy structure and components
- Implement common security patterns with YAML policies
- Organize and manage custom policy libraries
- Test and validate custom YAML policies

## 📝 Why Custom YAML Policies?

While Checkov provides 1000+ built-in checks, organizations often need:
- **Company-specific security requirements**
- **Industry-specific compliance rules**
- **Custom naming conventions and tagging policies**
- **Business logic validation**
- **Integration with internal standards**

YAML policies offer:
- ✅ **Simplicity**: No programming knowledge required
- ✅ **Readability**: Easy to understand and maintain
- ✅ **Version Control**: Track changes like code
- ✅ **Collaboration**: Non-developers can contribute

## 🏗️ YAML Policy Structure

### Basic Policy Template
```yaml
metadata:
  id: "CKV2_CUSTOM_1"
  name: "Custom policy name"
  category: "CUSTOM"
  
definition:
  cond_type: "attribute"
  resource_types:
    - "aws_s3_bucket"
  attribute: "tags.Environment"
  operator: "exists"
```

### Complete Policy Components

```yaml
metadata:
  # Required fields
  id: "CKV2_CUSTOM_1"                    # Unique identifier
  name: "Ensure S3 bucket has Environment tag"  # Human-readable name
  category: "TAGGING"                    # Policy category
  
  # Optional fields
  severity: "MEDIUM"                     # LOW, MEDIUM, HIGH, CRITICAL
  frameworks: ["CUSTOM"]                 # Compliance frameworks
  description: "All S3 buckets must have Environment tag"
  guidelines: "Add Environment tag with values: dev, staging, prod"
  
definition:
  # Condition type
  cond_type: "attribute"                 # attribute, connection, filter
  
  # Target resources
  resource_types:
    - "aws_s3_bucket"
    - "aws_s3_bucket_*"                 # Wildcard support
  
  # Attribute path
  attribute: "tags.Environment"          # Dot notation for nested attributes
  
  # Operator
  operator: "exists"                     # exists, equals, not_equals, etc.
  
  # Optional: Expected value
  # value: ["dev", "staging", "prod"]
```

## 🔧 Common Operators and Conditions

### 1. **Attribute Operators**

| Operator | Description | Example |
|----------|-------------|---------|
| `exists` | Attribute must exist | `tags.Environment` exists |
| `not_exists` | Attribute must not exist | `public_access_block` not exists |
| `equals` | Exact match | `encryption` equals `true` |
| `not_equals` | Not equal to | `instance_type` not equals `t2.nano` |
| `contains` | Contains substring | `name` contains `prod` |
| `not_contains` | Does not contain | `cidr_blocks` not contains `0.0.0.0/0` |
| `starting_with` | Starts with string | `bucket` starting with `company-` |
| `ending_with` | Ends with string | `name` ending with `-encrypted` |
| `regex_match` | Regex pattern | `name` regex matches `^[a-z0-9-]+$` |
| `greater_than` | Numeric comparison | `backup_retention_period` > 7 |
| `less_than` | Numeric comparison | `allocated_storage` < 1000 |

### 2. **Complex Conditions**

```yaml
# AND condition
definition:
  cond_type: "and"
  conditions:
    - cond_type: "attribute"
      attribute: "tags.Environment"
      operator: "exists"
    - cond_type: "attribute"
      attribute: "tags.Owner"
      operator: "exists"

# OR condition  
definition:
  cond_type: "or"
  conditions:
    - cond_type: "attribute"
      attribute: "instance_type"
      operator: "equals"
      value: "t3.micro"
    - cond_type: "attribute"
      attribute: "instance_type"
      operator: "equals"
      value: "t3.small"
```

## 📁 Folder Structure and Naming Conventions

### Recommended Directory Structure
```
custom-policies/
├── aws/
│   ├── compute/
│   │   ├── ec2-tagging.yaml
│   │   ├── ec2-security.yaml
│   │   └── lambda-config.yaml
│   ├── storage/
│   │   ├── s3-encryption.yaml
│   │   ├── s3-tagging.yaml
│   │   └── ebs-encryption.yaml
│   ├── networking/
│   │   ├── vpc-flow-logs.yaml
│   │   ├── security-groups.yaml
│   │   └── nacl-rules.yaml
│   └── database/
│       ├── rds-encryption.yaml
│       ├── rds-backup.yaml
│       └── dynamodb-encryption.yaml
├── azure/
├── gcp/
└── kubernetes/
```

### Naming Conventions
- **File names**: `service-requirement.yaml` (e.g., `s3-tagging.yaml`)
- **Policy IDs**: `CKV2_CUSTOM_[NUMBER]` or `CKV2_[ORG]_[NUMBER]`
- **Categories**: Use uppercase (TAGGING, ENCRYPTION, NETWORKING)

## 🏷️ Example: Tagging Policy

### Deny Untagged Resources

```yaml
# File: aws/tagging/require-environment-tag.yaml
metadata:
  id: "CKV2_CUSTOM_1"
  name: "Ensure all AWS resources have Environment tag"
  category: "TAGGING"
  severity: "MEDIUM"
  frameworks: ["CUSTOM", "COMPANY_POLICY"]
  description: "All AWS resources must have an Environment tag with valid values"
  guidelines: |
    Add Environment tag with one of these values:
    - dev: Development environment
    - staging: Staging/testing environment  
    - prod: Production environment

definition:
  cond_type: "and"
  conditions:
    - cond_type: "attribute"
      resource_types:
        - "aws_*"  # Apply to all AWS resources
      attribute: "tags.Environment"
      operator: "exists"
    - cond_type: "attribute"
      resource_types:
        - "aws_*"
      attribute: "tags.Environment"
      operator: "within"
      value: ["dev", "staging", "prod"]
```

### Multiple Required Tags

```yaml
# File: aws/tagging/required-tags.yaml
metadata:
  id: "CKV2_CUSTOM_2"
  name: "Ensure resources have required tags"
  category: "TAGGING"
  severity: "HIGH"
  
definition:
  cond_type: "and"
  conditions:
    # Environment tag
    - cond_type: "attribute"
      resource_types: ["aws_*"]
      attribute: "tags.Environment"
      operator: "exists"
    
    # Owner tag
    - cond_type: "attribute"
      resource_types: ["aws_*"]
      attribute: "tags.Owner"
      operator: "exists"
    
    # Project tag
    - cond_type: "attribute"
      resource_types: ["aws_*"]
      attribute: "tags.Project"
      operator: "exists"
    
    # CostCenter tag
    - cond_type: "attribute"
      resource_types: ["aws_*"]
      attribute: "tags.CostCenter"
      operator: "exists"
```

## 🔒 Example: Security Policies

### S3 Bucket Naming Convention

```yaml
# File: aws/storage/s3-naming-convention.yaml
metadata:
  id: "CKV2_CUSTOM_3"
  name: "Ensure S3 bucket follows naming convention"
  category: "NAMING"
  severity: "MEDIUM"
  description: "S3 buckets must follow company naming convention"
  guidelines: "Bucket names must start with company prefix and environment"

definition:
  cond_type: "or"
  conditions:
    # Development buckets
    - cond_type: "attribute"
      resource_types: ["aws_s3_bucket"]
      attribute: "bucket"
      operator: "regex_match"
      value: "^mycompany-dev-[a-z0-9-]+$"
    
    # Staging buckets
    - cond_type: "attribute"
      resource_types: ["aws_s3_bucket"]
      attribute: "bucket"
      operator: "regex_match"
      value: "^mycompany-staging-[a-z0-9-]+$"
    
    # Production buckets
    - cond_type: "attribute"
      resource_types: ["aws_s3_bucket"]
      attribute: "bucket"
      operator: "regex_match"
      value: "^mycompany-prod-[a-z0-9-]+$"
```

### Security Group Restrictions

```yaml
# File: aws/networking/security-group-restrictions.yaml
metadata:
  id: "CKV2_CUSTOM_4"
  name: "Ensure security groups don't allow unrestricted access"
  category: "NETWORKING"
  severity: "CRITICAL"
  description: "Security groups must not allow 0.0.0.0/0 access on sensitive ports"

definition:
  cond_type: "and"
  conditions:
    # No unrestricted SSH (port 22)
    - cond_type: "attribute"
      resource_types: ["aws_security_group", "aws_security_group_rule"]
      attribute: "ingress.*.cidr_blocks"
      operator: "not_contains"
      value: "0.0.0.0/0"
      
    # No unrestricted RDP (port 3389)  
    - cond_type: "filter"
      resource_types: ["aws_security_group"]
      attribute: "ingress"
      operator: "none_match"
      value:
        - from_port: 3389
          to_port: 3389
          cidr_blocks: ["0.0.0.0/0"]
```

## 🧪 Testing and Validating Custom Policies

### 1. **Create Test Terraform Files**

Create test cases for your policies:

```hcl
# test-cases/s3-tagging-test.tf

# This should PASS the tagging policy
resource "aws_s3_bucket" "compliant" {
  bucket = "mycompany-dev-test-bucket"
  
  tags = {
    Environment = "dev"
    Owner       = "platform-team"
    Project     = "checkov-learning"
    CostCenter  = "engineering"
  }
}

# This should FAIL the tagging policy
resource "aws_s3_bucket" "non_compliant" {
  bucket = "random-bucket-name"
  
  tags = {
    Name = "test-bucket"
    # Missing required tags
  }
}
```

### 2. **Run Tests**

```bash
# Test specific policy
checkov -f test-cases/s3-tagging-test.tf \
  --external-checks-dir custom-policies/ \
  --check CKV2_CUSTOM_1

# Test all custom policies
checkov -d test-cases/ \
  --external-checks-dir custom-policies/ \
  --framework terraform
```

### 3. **Validation Script**

Create a validation script:

```bash
#!/bin/bash
# validate-policies.sh

POLICY_DIR="custom-policies"
TEST_DIR="test-cases"

echo "🧪 Testing custom Checkov policies..."

# Test each policy file
for policy_file in $(find $POLICY_DIR -name "*.yaml"); do
    echo "Testing policy: $policy_file"
    
    # Extract policy ID
    policy_id=$(grep "id:" $policy_file | cut -d'"' -f2)
    
    # Run test
    checkov -d $TEST_DIR \
      --external-checks-dir $POLICY_DIR \
      --check $policy_id \
      --compact
    
    echo "---"
done

echo "✅ Policy validation complete!"
```

## 📋 Advanced YAML Policy Examples

### 1. **Resource Dependency Check**

```yaml
# File: aws/compute/ec2-security-group.yaml
metadata:
  id: "CKV2_CUSTOM_5"
  name: "Ensure EC2 instances have security groups attached"
  category: "NETWORKING"
  severity: "HIGH"

definition:
  cond_type: "connection"
  resource_types: ["aws_instance"]
  connected_resource_types: ["aws_security_group"]
  operator: "exists"
```

### 2. **Conditional Logic Based on Environment**

```yaml
# File: aws/database/rds-multi-az.yaml
metadata:
  id: "CKV2_CUSTOM_6"
  name: "Ensure production RDS instances are Multi-AZ"
  category: "AVAILABILITY"
  severity: "HIGH"

definition:
  cond_type: "filter"
  resource_types: ["aws_db_instance"]
  attribute: "tags.Environment"
  operator: "equals"
  value: "prod"
  
  # Then check Multi-AZ
  then:
    cond_type: "attribute"
    attribute: "multi_az"
    operator: "equals"
    value: true
```

### 3. **Complex Attribute Validation**

```yaml
# File: aws/storage/ebs-volume-size.yaml
metadata:
  id: "CKV2_CUSTOM_7"
  name: "Ensure EBS volumes are appropriately sized"
  category: "COST_OPTIMIZATION"
  severity: "MEDIUM"

definition:
  cond_type: "and"
  conditions:
    # Minimum size
    - cond_type: "attribute"
      resource_types: ["aws_ebs_volume"]
      attribute: "size"
      operator: "greater_than_or_equal"
      value: 8
    
    # Maximum size (prevent oversized volumes)
    - cond_type: "attribute"
      resource_types: ["aws_ebs_volume"]
      attribute: "size"
      operator: "less_than_or_equal"
      value: 1000
```

## 🔄 Using Custom Policies

### 1. **Command Line Usage**

```bash
# Use external checks directory
checkov -d . --external-checks-dir ./custom-policies/

# Combine with built-in checks
checkov -d . \
  --external-checks-dir ./custom-policies/ \
  --framework terraform

# Run only custom checks
checkov -d . \
  --external-checks-dir ./custom-policies/ \
  --check CKV2_CUSTOM_*
```

### 2. **Configuration File**

Create `.checkov.yml`:

```yaml
# .checkov.yml
framework:
  - terraform

external-checks-dir:
  - ./custom-policies/aws/
  - ./custom-policies/shared/

# Skip built-in checks if only using custom
skip-check:
  - CKV_AWS_*  # Skip all built-in AWS checks

# Or combine with built-in
check:
  - CKV2_CUSTOM_*  # Only custom checks
  - CKV_AWS_18     # Plus specific built-in checks
```

### 3. **CI/CD Integration**

```yaml
# .github/workflows/checkov-custom.yml
name: Custom Policy Validation

on: [push, pull_request]

jobs:
  checkov:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Checkov with custom policies
        uses: bridgecrewio/checkov-action@master
        with:
          directory: .
          external_checks_dir: ./custom-policies/
          framework: terraform
          output_format: sarif
          output_file_path: results.sarif
```

## 🎯 Next Steps

You now know how to:
- ✅ Create custom YAML policies for specific requirements
- ✅ Structure and organize policy libraries
- ✅ Test and validate custom policies
- ✅ Integrate custom policies into workflows

**Next:** [06 - Custom Policies with Python](../06_custom_policies_with_python/) →

## 📚 Quick Reference

### Basic YAML Policy Template
```yaml
metadata:
  id: "CKV2_CUSTOM_X"
  name: "Policy description"
  category: "CATEGORY"
  severity: "MEDIUM"

definition:
  cond_type: "attribute"
  resource_types: ["aws_resource_type"]
  attribute: "attribute.path"
  operator: "exists"
```

### Common Commands
```bash
# Test custom policies
checkov -d . --external-checks-dir ./custom-policies/

# Validate specific policy
checkov -f test.tf --external-checks-dir ./policies/ --check CKV2_CUSTOM_1

# List custom policies
checkov --external-checks-dir ./policies/ --list
```

---

**🎉 Great work!** You can now create powerful custom security policies using YAML. Next, we'll explore even more advanced capabilities with Python-based policies.
