metadata:
  id: "CKV2_CUSTOM_1"
  name: "Ensure all AWS resources have Environment tag"
  category: "TAGGING"
  severity: "MEDIUM"
  frameworks: ["CUSTOM", "COMPANY_POLICY"]
  description: "All AWS resources must have an Environment tag with valid values"
  guidelines: |
    Add Environment tag with one of these values:
    - dev: Development environment
    - staging: Staging/testing environment  
    - prod: Production environment

definition:
  cond_type: "and"
  conditions:
    - cond_type: "attribute"
      resource_types:
        - "aws_s3_bucket"
        - "aws_instance"
        - "aws_db_instance"
        - "aws_lambda_function"
        - "aws_ecs_service"
        - "aws_rds_cluster"
      attribute: "tags.Environment"
      operator: "exists"
    - cond_type: "attribute"
      resource_types:
        - "aws_s3_bucket"
        - "aws_instance"
        - "aws_db_instance"
        - "aws_lambda_function"
        - "aws_ecs_service"
        - "aws_rds_cluster"
      attribute: "tags.Environment"
      operator: "within"
      value: ["dev", "staging", "prod"]
