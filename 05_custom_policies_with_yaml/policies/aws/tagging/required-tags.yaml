metadata:
  id: "CKV2_CUSTOM_2"
  name: "Ensure resources have all required tags"
  category: "TAGGING"
  severity: "HIGH"
  frameworks: ["CUSTOM", "COMPANY_POLICY"]
  description: "All AWS resources must have Environment, Owner, Project, and CostCenter tags"
  guidelines: |
    Required tags for all resources:
    - Environment: dev, staging, or prod
    - Owner: Team or individual responsible
    - Project: Project name or identifier
    - CostCenter: Cost center for billing

definition:
  cond_type: "and"
  conditions:
    # Environment tag
    - cond_type: "attribute"
      resource_types: 
        - "aws_s3_bucket"
        - "aws_instance"
        - "aws_db_instance"
        - "aws_lambda_function"
        - "aws_ecs_service"
        - "aws_rds_cluster"
        - "aws_vpc"
        - "aws_security_group"
      attribute: "tags.Environment"
      operator: "exists"
    
    # Owner tag
    - cond_type: "attribute"
      resource_types: 
        - "aws_s3_bucket"
        - "aws_instance"
        - "aws_db_instance"
        - "aws_lambda_function"
        - "aws_ecs_service"
        - "aws_rds_cluster"
        - "aws_vpc"
        - "aws_security_group"
      attribute: "tags.Owner"
      operator: "exists"
    
    # Project tag
    - cond_type: "attribute"
      resource_types: 
        - "aws_s3_bucket"
        - "aws_instance"
        - "aws_db_instance"
        - "aws_lambda_function"
        - "aws_ecs_service"
        - "aws_rds_cluster"
        - "aws_vpc"
        - "aws_security_group"
      attribute: "tags.Project"
      operator: "exists"
    
    # CostCenter tag
    - cond_type: "attribute"
      resource_types: 
        - "aws_s3_bucket"
        - "aws_instance"
        - "aws_db_instance"
        - "aws_lambda_function"
        - "aws_ecs_service"
        - "aws_rds_cluster"
        - "aws_vpc"
        - "aws_security_group"
      attribute: "tags.CostCenter"
      operator: "exists"
