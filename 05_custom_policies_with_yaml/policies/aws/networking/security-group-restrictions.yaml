metadata:
  id: "CKV2_CUSTOM_4"
  name: "Ensure security groups follow company restrictions"
  category: "NETWORKING"
  severity: "CRITICAL"
  frameworks: ["CUSTOM", "COMPANY_POLICY", "CIS"]
  description: "Security groups must not allow unrestricted access on sensitive ports"
  guidelines: |
    Security group restrictions:
    - No 0.0.0.0/0 access on SSH (port 22)
    - No 0.0.0.0/0 access on RDP (port 3389)
    - No 0.0.0.0/0 access on database ports (3306, 5432, 1433)
    - Use specific CIDR blocks or security group references

definition:
  cond_type: "and"
  conditions:
    # No unrestricted SSH access
    - cond_type: "attribute"
      resource_types: ["aws_security_group"]
      attribute: "ingress"
      operator: "none_match"
      value:
        from_port: 22
        to_port: 22
        cidr_blocks: ["0.0.0.0/0"]
    
    # No unrestricted RDP access
    - cond_type: "attribute"
      resource_types: ["aws_security_group"]
      attribute: "ingress"
      operator: "none_match"
      value:
        from_port: 3389
        to_port: 3389
        cidr_blocks: ["0.0.0.0/0"]
    
    # No unrestricted MySQL access
    - cond_type: "attribute"
      resource_types: ["aws_security_group"]
      attribute: "ingress"
      operator: "none_match"
      value:
        from_port: 3306
        to_port: 3306
        cidr_blocks: ["0.0.0.0/0"]
    
    # No unrestricted PostgreSQL access
    - cond_type: "attribute"
      resource_types: ["aws_security_group"]
      attribute: "ingress"
      operator: "none_match"
      value:
        from_port: 5432
        to_port: 5432
        cidr_blocks: ["0.0.0.0/0"]
