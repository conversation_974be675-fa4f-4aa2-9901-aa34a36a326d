metadata:
  id: "CKV2_CUSTOM_3"
  name: "Ensure S3 bucket follows naming convention"
  category: "NAMING"
  severity: "MEDIUM"
  frameworks: ["CUSTOM", "COMPANY_POLICY"]
  description: "S3 buckets must follow company naming convention with environment prefix"
  guidelines: |
    S3 bucket naming convention:
    - Development: mycompany-dev-[service]-[purpose]
    - Staging: mycompany-staging-[service]-[purpose]
    - Production: mycompany-prod-[service]-[purpose]
    
    Examples:
    - mycompany-dev-webapp-logs
    - mycompany-prod-api-backups
    - mycompany-staging-ml-datasets

definition:
  cond_type: "or"
  conditions:
    # Development buckets
    - cond_type: "attribute"
      resource_types: ["aws_s3_bucket"]
      attribute: "bucket"
      operator: "regex_match"
      value: "^mycompany-dev-[a-z0-9-]+$"
    
    # Staging buckets
    - cond_type: "attribute"
      resource_types: ["aws_s3_bucket"]
      attribute: "bucket"
      operator: "regex_match"
      value: "^mycompany-staging-[a-z0-9-]+$"
    
    # Production buckets
    - cond_type: "attribute"
      resource_types: ["aws_s3_bucket"]
      attribute: "bucket"
      operator: "regex_match"
      value: "^mycompany-prod-[a-z0-9-]+$"
