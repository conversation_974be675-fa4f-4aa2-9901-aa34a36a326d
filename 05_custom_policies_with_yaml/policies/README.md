# Custom Checkov Policies

This directory contains custom YAML-based Checkov policies for enforcing organization-specific security and compliance requirements.

## 📁 Directory Structure

```
policies/
├── aws/
│   ├── tagging/
│   │   ├── require-environment-tag.yaml
│   │   └── required-tags.yaml
│   ├── storage/
│   │   └── s3-naming-convention.yaml
│   └── networking/
│       └── security-group-restrictions.yaml
└── README.md
```

## 🏷️ Policy Categories

### Tagging Policies
- **CKV2_CUSTOM_1**: Require Environment tag with valid values
- **CKV2_CUSTOM_2**: Require all mandatory tags (Environment, Owner, Project, CostCenter)

### Storage Policies  
- **CKV2_CUSTOM_3**: Enforce S3 bucket naming conventions

### Networking Policies
- **CKV2_CUSTOM_4**: Restrict security group rules to prevent unrestricted access

## 🚀 Usage

### Command Line
```bash
# Test all custom policies
checkov -d . --external-checks-dir ./policies/

# Test specific policy
checkov -d . --external-checks-dir ./policies/ --check CKV2_CUSTOM_1

# Combine with built-in checks
checkov -d . --external-checks-dir ./policies/ --framework terraform
```

### Configuration File (.checkov.yml)
```yaml
framework:
  - terraform

external-checks-dir:
  - ./policies/

# Run only custom policies
check:
  - CKV2_CUSTOM_*
```

## 🧪 Testing

Use the validation script to test all policies:

```bash
# Run validation script
./validate-policies.sh

# Manual testing
checkov -f test-cases/s3-tagging-test.tf --external-checks-dir ./policies/
```

## 📋 Policy Template

Use this template for creating new policies:

```yaml
metadata:
  id: "CKV2_CUSTOM_X"
  name: "Policy description"
  category: "CATEGORY"
  severity: "MEDIUM"
  frameworks: ["CUSTOM"]
  description: "Detailed description"
  guidelines: "Implementation guidelines"

definition:
  cond_type: "attribute"
  resource_types: ["aws_resource_type"]
  attribute: "attribute.path"
  operator: "exists"
```

## 🔧 Adding New Policies

1. Create YAML file in appropriate category directory
2. Use unique policy ID (CKV2_CUSTOM_X)
3. Add test cases in `../test-cases/`
4. Run validation script to test
5. Update this README with new policy information

## 📊 Policy Severity Levels

- **CRITICAL**: Security vulnerabilities that could lead to data breaches
- **HIGH**: Important security misconfigurations
- **MEDIUM**: Best practice violations
- **LOW**: Informational or optimization recommendations

## 🏢 Compliance Frameworks

Policies are mapped to relevant compliance frameworks:
- **CUSTOM**: Organization-specific requirements
- **COMPANY_POLICY**: Internal security policies
- **CIS**: Center for Internet Security benchmarks
- **NIST**: NIST Cybersecurity Framework
- **SOC2**: SOC 2 compliance requirements

## 📝 Contributing

When adding new policies:
1. Follow the naming convention
2. Include comprehensive metadata
3. Add clear guidelines
4. Create test cases
5. Validate before committing
