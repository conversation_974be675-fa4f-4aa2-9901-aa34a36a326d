# Test cases for security group policies
# This file contains both compliant and non-compliant security groups

# COMPLIANT: Restrictive security group
resource "aws_security_group" "compliant_web_sg" {
  name_prefix = "web-sg-"
  description = "Security group for web servers"
  
  # Allow HTTP from specific CIDR
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/16"]
  }
  
  # Allow HTTPS from specific CIDR
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/16"]
  }
  
  # Restrictive egress
  egress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = {
    Environment = "prod"
    Owner       = "security-team"
    Project     = "web-application"
    CostCenter  = "engineering"
  }
}

# NON-COMPLIANT: Allows unrestricted SSH access
resource "aws_security_group" "ssh_open_to_world" {
  name_prefix = "ssh-sg-"
  description = "Security group with unrestricted SSH - BAD!"
  
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # This should fail the policy
  }
  
  tags = {
    Environment = "dev"
    Owner       = "developer"
    Project     = "testing"
    CostCenter  = "engineering"
  }
}

# NON-COMPLIANT: Allows unrestricted RDP access
resource "aws_security_group" "rdp_open_to_world" {
  name_prefix = "rdp-sg-"
  description = "Security group with unrestricted RDP - BAD!"
  
  ingress {
    from_port   = 3389
    to_port     = 3389
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # This should fail the policy
  }
  
  tags = {
    Environment = "dev"
    Owner       = "developer"
    Project     = "testing"
    CostCenter  = "engineering"
  }
}

# NON-COMPLIANT: Allows unrestricted database access
resource "aws_security_group" "database_open_to_world" {
  name_prefix = "db-sg-"
  description = "Security group with unrestricted database access - BAD!"
  
  # MySQL port open to world
  ingress {
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # This should fail the policy
  }
  
  # PostgreSQL port open to world
  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # This should fail the policy
  }
  
  tags = {
    Environment = "dev"
    Owner       = "developer"
    Project     = "testing"
    CostCenter  = "engineering"
  }
}

# COMPLIANT: Database security group with proper restrictions
resource "aws_security_group" "compliant_database_sg" {
  name_prefix = "db-sg-"
  description = "Properly configured database security group"
  
  # MySQL access only from application subnet
  ingress {
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["********/24"]  # Specific subnet only
  }
  
  # No egress rules (default deny)
  
  tags = {
    Environment = "prod"
    Owner       = "database-team"
    Project     = "api-service"
    CostCenter  = "engineering"
  }
}

# COMPLIANT: Security group using security group references instead of CIDR
resource "aws_security_group" "app_sg" {
  name_prefix = "app-sg-"
  description = "Application security group"
  
  tags = {
    Environment = "prod"
    Owner       = "backend-team"
    Project     = "api-service"
    CostCenter  = "engineering"
  }
}

resource "aws_security_group_rule" "app_to_db" {
  type                     = "egress"
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.compliant_database_sg.id
  security_group_id        = aws_security_group.app_sg.id
}
