# Test cases for S3 tagging policies
# This file contains both compliant and non-compliant resources for testing

# COMPLIANT: This should PASS all tagging policies
resource "aws_s3_bucket" "compliant_bucket" {
  bucket = "mycompany-dev-webapp-logs"
  
  tags = {
    Environment = "dev"
    Owner       = "platform-team"
    Project     = "checkov-learning"
    CostCenter  = "engineering"
  }
}

# NON-COMPLIANT: Missing required tags
resource "aws_s3_bucket" "missing_tags" {
  bucket = "mycompany-staging-api-data"
  
  tags = {
    Name = "test-bucket"
    # Missing: Environment, Owner, Project, CostCenter
  }
}

# NON-COMPLIANT: Wrong naming convention
resource "aws_s3_bucket" "wrong_naming" {
  bucket = "random-bucket-name-123"
  
  tags = {
    Environment = "dev"
    Owner       = "platform-team"
    Project     = "checkov-learning"
    CostCenter  = "engineering"
  }
}

# NON-COMPLIANT: Invalid Environment value
resource "aws_s3_bucket" "invalid_environment" {
  bucket = "mycompany-prod-ml-models"
  
  tags = {
    Environment = "development"  # Should be "dev", not "development"
    Owner       = "ml-team"
    Project     = "machine-learning"
    CostCenter  = "research"
  }
}

# COMPLIANT: Production bucket with proper tags and naming
resource "aws_s3_bucket" "compliant_prod_bucket" {
  bucket = "mycompany-prod-api-backups"
  
  tags = {
    Environment = "prod"
    Owner       = "backend-team"
    Project     = "api-service"
    CostCenter  = "engineering"
  }
}

# Test EC2 instance tagging
resource "aws_instance" "compliant_instance" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  tags = {
    Environment = "staging"
    Owner       = "devops-team"
    Project     = "web-application"
    CostCenter  = "engineering"
    Name        = "web-server-staging"
  }
}

# NON-COMPLIANT: EC2 instance missing tags
resource "aws_instance" "non_compliant_instance" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  tags = {
    Name = "test-server"
    # Missing required tags
  }
}
