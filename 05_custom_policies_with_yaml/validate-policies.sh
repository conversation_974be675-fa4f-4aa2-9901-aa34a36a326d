#!/bin/bash

# Custom Policy Validation Script
# This script tests all custom YAML policies against test cases

set -e

POLICY_DIR="policies"
TEST_DIR="test-cases"
RESULTS_DIR="results"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create results directory
mkdir -p $RESULTS_DIR

echo -e "${BLUE}🧪 Testing Custom Checkov Policies${NC}"
echo "=================================="

# Check if checkov is installed
if ! command -v checkov &> /dev/null; then
    echo -e "${RED}❌ Checkov is not installed. Please install it first:${NC}"
    echo "pip install checkov"
    exit 1
fi

# Check if policy directory exists
if [ ! -d "$POLICY_DIR" ]; then
    echo -e "${RED}❌ Policy directory '$POLICY_DIR' not found${NC}"
    exit 1
fi

# Check if test directory exists
if [ ! -d "$TEST_DIR" ]; then
    echo -e "${RED}❌ Test directory '$TEST_DIR' not found${NC}"
    exit 1
fi

# Function to extract policy ID from YAML file
extract_policy_id() {
    local file=$1
    grep "id:" "$file" | head -1 | sed 's/.*id: *"\([^"]*\)".*/\1/'
}

# Function to extract policy name from YAML file
extract_policy_name() {
    local file=$1
    grep "name:" "$file" | head -1 | sed 's/.*name: *"\([^"]*\)".*/\1/'
}

# Test individual policy
test_policy() {
    local policy_file=$1
    local policy_id=$2
    local policy_name=$3
    
    echo -e "\n${YELLOW}Testing Policy: $policy_id${NC}"
    echo "Name: $policy_name"
    echo "File: $policy_file"
    echo "---"
    
    # Run checkov with the specific policy
    local output_file="$RESULTS_DIR/${policy_id}_results.json"
    
    if checkov -d $TEST_DIR \
        --external-checks-dir $POLICY_DIR \
        --check $policy_id \
        --output json \
        --output-file-path $output_file \
        --quiet; then
        
        # Parse results
        local failed_checks=$(jq '.results.failed_checks | length' $output_file 2>/dev/null || echo "0")
        local passed_checks=$(jq '.results.passed_checks | length' $output_file 2>/dev/null || echo "0")
        
        echo -e "${GREEN}✅ Policy executed successfully${NC}"
        echo "   Passed: $passed_checks"
        echo "   Failed: $failed_checks"
        
        # Show failed checks if any
        if [ "$failed_checks" -gt 0 ]; then
            echo -e "${YELLOW}   Failed resources:${NC}"
            jq -r '.results.failed_checks[] | "     - " + .resource + " (" + .file_path + ")"' $output_file 2>/dev/null || echo "     Unable to parse failed checks"
        fi
        
        return 0
    else
        echo -e "${RED}❌ Policy execution failed${NC}"
        return 1
    fi
}

# Main testing loop
total_policies=0
successful_policies=0
failed_policies=0

echo -e "\n${BLUE}Scanning for policy files...${NC}"

# Find all YAML policy files
while IFS= read -r -d '' policy_file; do
    total_policies=$((total_policies + 1))
    
    # Extract policy metadata
    policy_id=$(extract_policy_id "$policy_file")
    policy_name=$(extract_policy_name "$policy_file")
    
    if [ -z "$policy_id" ]; then
        echo -e "${RED}❌ Could not extract policy ID from $policy_file${NC}"
        failed_policies=$((failed_policies + 1))
        continue
    fi
    
    # Test the policy
    if test_policy "$policy_file" "$policy_id" "$policy_name"; then
        successful_policies=$((successful_policies + 1))
    else
        failed_policies=$((failed_policies + 1))
    fi
    
done < <(find $POLICY_DIR -name "*.yaml" -print0)

# Summary
echo -e "\n${BLUE}=================================="
echo "Test Summary"
echo "==================================${NC}"
echo "Total policies tested: $total_policies"
echo -e "Successful: ${GREEN}$successful_policies${NC}"
echo -e "Failed: ${RED}$failed_policies${NC}"

# Test all policies together
echo -e "\n${BLUE}Running comprehensive test with all policies...${NC}"
comprehensive_output="$RESULTS_DIR/comprehensive_results.json"

if checkov -d $TEST_DIR \
    --external-checks-dir $POLICY_DIR \
    --framework terraform \
    --output json \
    --output-file-path $comprehensive_output \
    --compact; then
    
    echo -e "${GREEN}✅ Comprehensive test completed${NC}"
    
    # Generate summary report
    total_failed=$(jq '.results.failed_checks | length' $comprehensive_output 2>/dev/null || echo "0")
    total_passed=$(jq '.results.passed_checks | length' $comprehensive_output 2>/dev/null || echo "0")
    
    echo "Total checks run: $((total_failed + total_passed))"
    echo -e "Passed: ${GREEN}$total_passed${NC}"
    echo -e "Failed: ${RED}$total_failed${NC}"
    
    # Generate HTML report if possible
    if command -v checkov &> /dev/null; then
        echo -e "\n${BLUE}Generating HTML report...${NC}"
        checkov -d $TEST_DIR \
            --external-checks-dir $POLICY_DIR \
            --framework terraform \
            --output html \
            --output-file-path $RESULTS_DIR/comprehensive_report.html \
            --quiet
        echo -e "${GREEN}✅ HTML report generated: $RESULTS_DIR/comprehensive_report.html${NC}"
    fi
else
    echo -e "${RED}❌ Comprehensive test failed${NC}"
    failed_policies=$((failed_policies + 1))
fi

# Final status
echo -e "\n${BLUE}==================================${NC}"
if [ $failed_policies -eq 0 ]; then
    echo -e "${GREEN}🎉 All policy tests passed!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some policy tests failed. Check the output above for details.${NC}"
    exit 1
fi
