# 06 - Custom Policies with Python

## 🎯 Learning Objectives
- Create advanced custom Checkov policies using Python
- Understand the BaseCheck class and its methods
- Implement complex business logic and validation rules
- Handle cross-resource dependencies and relationships
- Register and test Python-based custom checks

## 🐍 Why Python Policies?

While YAML policies are great for simple attribute checks, Python policies enable:
- **Complex Logic**: Multi-step validation and conditional checks
- **Cross-Resource Analysis**: Validate relationships between resources
- **Dynamic Validation**: Runtime calculations and comparisons
- **Advanced String Processing**: Regex, parsing, and transformation
- **External API Integration**: Validate against external systems
- **Custom Data Structures**: Handle complex nested configurations

## 🏗️ Python Policy Structure

### Basic Policy Template

```python
from checkov.common.models.enums import TrueOrFalse
from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck

class CustomCheck(BaseResourceCheck):
    def __init__(self):
        name = "Custom check description"
        id = "CKV_CUSTOM_1"
        supported_resources = ["aws_s3_bucket"]
        categories = ["ENCRYPTION"]
        super().__init__(name=name, id=id, categories=categories, supported_resources=supported_resources)

    def scan_resource_conf(self, conf):
        """
        Looks for encryption configuration on S3 bucket
        :param conf: aws_s3_bucket configuration
        :return: <CheckResult>
        """
        # Your validation logic here
        if "server_side_encryption_configuration" in conf:
            return CheckResult.PASSED
        return CheckResult.FAILED
```

### Complete Policy Components

```python
from checkov.common.models.enums import TrueOrFalse
from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck
from checkov.common.models.consts import ANY_VALUE

class AdvancedCustomCheck(BaseResourceCheck):
    def __init__(self):
        # Required attributes
        name = "Ensure S3 bucket has proper encryption and naming"
        id = "CKV_CUSTOM_1"
        supported_resources = ["aws_s3_bucket"]
        categories = ["ENCRYPTION", "NAMING"]
        
        # Optional attributes
        guideline = "S3 buckets must use KMS encryption and follow naming convention"
        
        super().__init__(
            name=name, 
            id=id, 
            categories=categories, 
            supported_resources=supported_resources,
            guideline=guideline
        )

    def scan_resource_conf(self, conf):
        """
        Main validation method
        :param conf: Resource configuration dictionary
        :return: CheckResult.PASSED or CheckResult.FAILED
        """
        # Validation logic
        if self._check_encryption(conf) and self._check_naming(conf):
            return CheckResult.PASSED
        return CheckResult.FAILED
    
    def _check_encryption(self, conf):
        """Helper method for encryption validation"""
        # Implementation details
        pass
    
    def _check_naming(self, conf):
        """Helper method for naming validation"""
        # Implementation details
        pass
```

## 🔧 BaseResourceCheck Methods and Properties

### Essential Methods

| Method | Purpose | Return Type |
|--------|---------|-------------|
| `scan_resource_conf(conf)` | Main validation logic | `CheckResult` |
| `get_resource_conf(conf)` | Extract resource config | `dict` |
| `get_evaluated_keys()` | Keys that were evaluated | `list` |

### Helper Properties

```python
class CustomCheck(BaseResourceCheck):
    def __init__(self):
        # Basic properties
        name = "Check description"
        id = "CKV_CUSTOM_1"
        supported_resources = ["aws_s3_bucket", "aws_instance"]
        categories = ["ENCRYPTION"]
        
        # Advanced properties
        guideline = "Implementation guidance"
        severity = "HIGH"  # LOW, MEDIUM, HIGH, CRITICAL
        bc_id = "BC_AWS_1"  # Bridgecrew ID mapping
        
        super().__init__(
            name=name,
            id=id,
            categories=categories,
            supported_resources=supported_resources,
            guideline=guideline
        )
```

## 📝 Example: IAM Policy Wildcard Check

Let's create a policy that flags IAM policies with wildcard permissions:

```python
# File: custom_checks/iam_wildcard_check.py
import json
from checkov.common.models.enums import TrueOrFalse
from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck
from checkov.common.models.consts import ANY_VALUE

class IAMWildcardCheck(BaseResourceCheck):
    def __init__(self):
        name = "Ensure IAM policies do not grant wildcard permissions"
        id = "CKV_CUSTOM_1"
        supported_resources = [
            "aws_iam_role_policy",
            "aws_iam_user_policy", 
            "aws_iam_group_policy",
            "aws_iam_policy"
        ]
        categories = ["IAM"]
        guideline = "IAM policies should use specific permissions instead of wildcards (*)"
        
        super().__init__(
            name=name,
            id=id,
            categories=categories,
            supported_resources=supported_resources,
            guideline=guideline
        )

    def scan_resource_conf(self, conf):
        """
        Scan IAM policy for wildcard permissions
        :param conf: IAM policy configuration
        :return: CheckResult
        """
        # Get policy document
        policy_doc = self._get_policy_document(conf)
        if not policy_doc:
            return CheckResult.UNKNOWN
        
        # Parse policy document
        try:
            if isinstance(policy_doc, str):
                policy = json.loads(policy_doc)
            else:
                policy = policy_doc
        except (json.JSONDecodeError, TypeError):
            return CheckResult.UNKNOWN
        
        # Check for wildcard permissions
        if self._has_wildcard_permissions(policy):
            return CheckResult.FAILED
            
        return CheckResult.PASSED
    
    def _get_policy_document(self, conf):
        """Extract policy document from configuration"""
        if "policy" in conf:
            return conf["policy"][0]
        return None
    
    def _has_wildcard_permissions(self, policy):
        """Check if policy contains wildcard permissions"""
        statements = policy.get("Statement", [])
        if not isinstance(statements, list):
            statements = [statements]
        
        for statement in statements:
            # Check Effect
            if statement.get("Effect") != "Allow":
                continue
            
            # Check Actions
            actions = statement.get("Action", [])
            if not isinstance(actions, list):
                actions = [actions]
            
            for action in actions:
                if action == "*" or action.endswith(":*"):
                    # Check if Resource is also wildcard (double wildcard = very bad)
                    resources = statement.get("Resource", [])
                    if not isinstance(resources, list):
                        resources = [resources]
                    
                    for resource in resources:
                        if resource == "*":
                            return True
        
        return False

# Register the check
check = IAMWildcardCheck()
```

## 🔗 Example: Cross-Resource Dependency Check

Check if EC2 instances have security groups attached:

```python
# File: custom_checks/ec2_security_group_check.py
from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck
from checkov.common.models.enums import TrueOrFalse

class EC2SecurityGroupCheck(BaseResourceCheck):
    def __init__(self):
        name = "Ensure EC2 instances have security groups attached"
        id = "CKV_CUSTOM_2"
        supported_resources = ["aws_instance"]
        categories = ["NETWORKING"]
        
        super().__init__(
            name=name,
            id=id,
            categories=categories,
            supported_resources=supported_resources
        )

    def scan_resource_conf(self, conf):
        """
        Check if EC2 instance has security groups
        :param conf: aws_instance configuration
        :return: CheckResult
        """
        # Check for vpc_security_group_ids
        if "vpc_security_group_ids" in conf:
            sg_ids = conf["vpc_security_group_ids"][0]
            if isinstance(sg_ids, list) and len(sg_ids) > 0:
                return CheckResult.PASSED
        
        # Check for security_groups (classic)
        if "security_groups" in conf:
            sgs = conf["security_groups"][0]
            if isinstance(sgs, list) and len(sgs) > 0:
                return CheckResult.PASSED
        
        # Check if security group is defined inline
        if self._has_inline_security_group(conf):
            return CheckResult.PASSED
        
        return CheckResult.FAILED
    
    def _has_inline_security_group(self, conf):
        """Check for inline security group definition"""
        # This would require graph-based analysis
        # For now, we'll assume no inline definition
        return False

check = EC2SecurityGroupCheck()
```

## 🧮 Example: Complex Business Logic

Validate RDS instance configuration based on environment:

```python
# File: custom_checks/rds_environment_check.py
from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck
from checkov.common.models.enums import TrueOrFalse

class RDSEnvironmentCheck(BaseResourceCheck):
    def __init__(self):
        name = "Ensure RDS configuration matches environment requirements"
        id = "CKV_CUSTOM_3"
        supported_resources = ["aws_db_instance"]
        categories = ["DATABASE"]
        guideline = """
        Environment-specific RDS requirements:
        - Production: Multi-AZ, encrypted, backup retention >= 7 days
        - Staging: Encrypted, backup retention >= 3 days
        - Development: Backup retention >= 1 day
        """
        
        super().__init__(
            name=name,
            id=id,
            categories=categories,
            supported_resources=supported_resources,
            guideline=guideline
        )

    def scan_resource_conf(self, conf):
        """
        Validate RDS configuration based on environment
        :param conf: aws_db_instance configuration
        :return: CheckResult
        """
        # Get environment from tags
        environment = self._get_environment(conf)
        if not environment:
            return CheckResult.UNKNOWN
        
        # Validate based on environment
        if environment.lower() == "prod":
            return self._validate_production(conf)
        elif environment.lower() == "staging":
            return self._validate_staging(conf)
        elif environment.lower() == "dev":
            return self._validate_development(conf)
        else:
            return CheckResult.UNKNOWN
    
    def _get_environment(self, conf):
        """Extract environment from tags"""
        tags = conf.get("tags", [{}])[0]
        return tags.get("Environment") or tags.get("environment")
    
    def _validate_production(self, conf):
        """Validate production RDS requirements"""
        checks = [
            self._check_multi_az(conf),
            self._check_encryption(conf),
            self._check_backup_retention(conf, min_days=7)
        ]
        
        return CheckResult.PASSED if all(checks) else CheckResult.FAILED
    
    def _validate_staging(self, conf):
        """Validate staging RDS requirements"""
        checks = [
            self._check_encryption(conf),
            self._check_backup_retention(conf, min_days=3)
        ]
        
        return CheckResult.PASSED if all(checks) else CheckResult.FAILED
    
    def _validate_development(self, conf):
        """Validate development RDS requirements"""
        return CheckResult.PASSED if self._check_backup_retention(conf, min_days=1) else CheckResult.FAILED
    
    def _check_multi_az(self, conf):
        """Check if Multi-AZ is enabled"""
        multi_az = conf.get("multi_az", [False])[0]
        return multi_az is True
    
    def _check_encryption(self, conf):
        """Check if storage encryption is enabled"""
        encrypted = conf.get("storage_encrypted", [False])[0]
        return encrypted is True
    
    def _check_backup_retention(self, conf, min_days):
        """Check backup retention period"""
        retention = conf.get("backup_retention_period", [0])[0]
        try:
            return int(retention) >= min_days
        except (ValueError, TypeError):
            return False

check = RDSEnvironmentCheck()
```

## 📁 Directory Structure and Registration

### Recommended Structure
```
custom-checks/
├── __init__.py
├── iam/
│   ├── __init__.py
│   ├── iam_wildcard_check.py
│   └── iam_password_policy_check.py
├── networking/
│   ├── __init__.py
│   ├── security_group_check.py
│   └── vpc_flow_logs_check.py
├── storage/
│   ├── __init__.py
│   ├── s3_encryption_check.py
│   └── ebs_encryption_check.py
└── database/
    ├── __init__.py
    └── rds_environment_check.py
```

### Registration Methods

#### Method 1: Direct Import
```python
# In your check file
from checkov.runner_filter import RunnerFilter
from checkov.terraform.runner import Runner

# Register check
check = CustomCheck()
```

#### Method 2: Plugin Registration
```python
# setup.py or __init__.py
from checkov.main import Checkov

def register_checks():
    from .iam.iam_wildcard_check import check as iam_check
    from .networking.security_group_check import check as sg_check
    
    # Checks are automatically registered when imported
    pass
```

## 🧪 Testing Python Policies

### Unit Testing Framework

```python
# File: tests/test_iam_wildcard_check.py
import unittest
from custom_checks.iam.iam_wildcard_check import IAMWildcardCheck

class TestIAMWildcardCheck(unittest.TestCase):
    def setUp(self):
        self.check = IAMWildcardCheck()
    
    def test_wildcard_action_and_resource_fails(self):
        """Test that wildcard action and resource fails"""
        conf = {
            "policy": ['''{
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Action": "*",
                        "Resource": "*"
                    }
                ]
            }''']
        }
        
        result = self.check.scan_resource_conf(conf)
        self.assertEqual(result, CheckResult.FAILED)
    
    def test_specific_permissions_pass(self):
        """Test that specific permissions pass"""
        conf = {
            "policy": ['''{
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Action": "s3:GetObject",
                        "Resource": "arn:aws:s3:::my-bucket/*"
                    }
                ]
            }''']
        }
        
        result = self.check.scan_resource_conf(conf)
        self.assertEqual(result, CheckResult.PASSED)

if __name__ == '__main__':
    unittest.main()
```

### Integration Testing

```bash
# Test with actual Terraform files
checkov -f test-terraform/iam-policy.tf \
  --external-checks-dir ./custom-checks/ \
  --check CKV_CUSTOM_1

# Test all custom checks
checkov -d test-terraform/ \
  --external-checks-dir ./custom-checks/ \
  --framework terraform
```

## 🚀 Using Python Policies

### Command Line Usage

```bash
# Use external checks directory
checkov -d . --external-checks-dir ./custom-checks/

# Specific Python check
checkov -d . --external-checks-dir ./custom-checks/ --check CKV_CUSTOM_1

# Combine with YAML policies
checkov -d . \
  --external-checks-dir ./custom-checks/ \
  --external-checks-dir ./yaml-policies/
```

### Configuration File

```yaml
# .checkov.yml
framework:
  - terraform

external-checks-dir:
  - ./custom-checks/
  - ./yaml-policies/

check:
  - CKV_CUSTOM_*  # All custom checks
```

## 🎯 Next Steps

You now know how to:
- ✅ Create sophisticated Python-based policies
- ✅ Implement complex business logic and validation
- ✅ Handle cross-resource dependencies
- ✅ Test and register custom Python checks

**Next:** [07 - CI/CD Integration](../07_ci_cd_integration/) →

## 📚 Quick Reference

### Basic Python Check Template
```python
from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck

class CustomCheck(BaseResourceCheck):
    def __init__(self):
        super().__init__(
            name="Check description",
            id="CKV_CUSTOM_1",
            categories=["CATEGORY"],
            supported_resources=["aws_resource"]
        )

    def scan_resource_conf(self, conf):
        # Your logic here
        return CheckResult.PASSED  # or CheckResult.FAILED
```

### Common Patterns
- **Configuration Access**: `conf.get("attribute", [default])[0]`
- **Tag Access**: `conf.get("tags", [{}])[0].get("TagName")`
- **List Handling**: Always check if value is list before iteration
- **JSON Parsing**: Use try/except for policy document parsing

---

**🎉 Outstanding!** You can now create powerful, complex security policies using Python. These policies can implement sophisticated business logic that goes far beyond simple attribute checks.
