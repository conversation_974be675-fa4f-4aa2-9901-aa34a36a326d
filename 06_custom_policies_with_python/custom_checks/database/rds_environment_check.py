"""
Custom Checkov check for environment-specific RDS configuration requirements
This check validates RDS instances based on their environment tag
"""

from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck
from checkov.common.models.enums import TrueOrFalse

class RDSEnvironmentCheck(BaseResourceCheck):
    def __init__(self):
        name = "Ensure RDS configuration matches environment requirements"
        id = "CKV_CUSTOM_3"
        supported_resources = ["aws_db_instance"]
        categories = ["DATABASE"]
        guideline = """
        Environment-specific RDS requirements:
        
        Production (Environment=prod):
        - Multi-AZ must be enabled
        - Storage encryption must be enabled
        - Backup retention >= 7 days
        - Deletion protection should be enabled
        
        Staging (Environment=staging):
        - Storage encryption must be enabled
        - Backup retention >= 3 days
        
        Development (Environment=dev):
        - Backup retention >= 1 day
        
        If no Environment tag is found, the check will be skipped.
        """
        
        super().__init__(
            name=name,
            id=id,
            categories=categories,
            supported_resources=supported_resources,
            guideline=guideline
        )

    def scan_resource_conf(self, conf):
        """
        Validate RDS configuration based on environment tag
        :param conf: aws_db_instance configuration
        :return: CheckResult
        """
        # Get environment from tags
        environment = self._get_environment(conf)
        if not environment:
            # If no environment tag, skip the check
            return CheckResult.UNKNOWN
        
        # Normalize environment value
        env = environment.lower().strip()
        
        # Validate based on environment
        if env in ["prod", "production"]:
            return self._validate_production(conf)
        elif env in ["staging", "stage"]:
            return self._validate_staging(conf)
        elif env in ["dev", "development"]:
            return self._validate_development(conf)
        else:
            # Unknown environment, skip check
            return CheckResult.UNKNOWN
    
    def _get_environment(self, conf):
        """Extract environment from tags"""
        tags = conf.get("tags", [{}])
        if not tags or not isinstance(tags, list):
            return None
        
        tag_dict = tags[0] if tags else {}
        
        # Try different common tag names for environment
        for env_key in ["Environment", "environment", "Env", "env"]:
            if env_key in tag_dict:
                return tag_dict[env_key]
        
        return None
    
    def _validate_production(self, conf):
        """Validate production RDS requirements"""
        checks = [
            ("Multi-AZ", self._check_multi_az(conf)),
            ("Encryption", self._check_encryption(conf)),
            ("Backup Retention >= 7 days", self._check_backup_retention(conf, min_days=7)),
            ("Deletion Protection", self._check_deletion_protection(conf))
        ]
        
        # All checks must pass for production
        failed_checks = [name for name, result in checks if not result]
        
        if failed_checks:
            # Could add more detailed error reporting here
            return CheckResult.FAILED
        
        return CheckResult.PASSED
    
    def _validate_staging(self, conf):
        """Validate staging RDS requirements"""
        checks = [
            ("Encryption", self._check_encryption(conf)),
            ("Backup Retention >= 3 days", self._check_backup_retention(conf, min_days=3))
        ]
        
        # All checks must pass for staging
        failed_checks = [name for name, result in checks if not result]
        
        return CheckResult.PASSED if not failed_checks else CheckResult.FAILED
    
    def _validate_development(self, conf):
        """Validate development RDS requirements"""
        # Only backup retention check for development
        return CheckResult.PASSED if self._check_backup_retention(conf, min_days=1) else CheckResult.FAILED
    
    def _check_multi_az(self, conf):
        """Check if Multi-AZ is enabled"""
        multi_az = conf.get("multi_az", [False])
        if isinstance(multi_az, list) and len(multi_az) > 0:
            return multi_az[0] is True
        return multi_az is True
    
    def _check_encryption(self, conf):
        """Check if storage encryption is enabled"""
        encrypted = conf.get("storage_encrypted", [False])
        if isinstance(encrypted, list) and len(encrypted) > 0:
            return encrypted[0] is True
        return encrypted is True
    
    def _check_backup_retention(self, conf, min_days):
        """Check backup retention period"""
        retention = conf.get("backup_retention_period", [0])
        
        if isinstance(retention, list) and len(retention) > 0:
            retention_value = retention[0]
        else:
            retention_value = retention
        
        try:
            return int(retention_value) >= min_days
        except (ValueError, TypeError):
            return False
    
    def _check_deletion_protection(self, conf):
        """Check if deletion protection is enabled"""
        deletion_protection = conf.get("deletion_protection", [False])
        if isinstance(deletion_protection, list) and len(deletion_protection) > 0:
            return deletion_protection[0] is True
        return deletion_protection is True

# Register the check
check = RDSEnvironmentCheck()
