"""
Custom Checkov check for IAM policies with wildcard permissions
This check flags IAM policies that grant wildcard (*) permissions on all resources
"""

import json
from checkov.common.models.enums import TrueOrFalse
from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck
from checkov.common.models.consts import ANY_VALUE

class IAMWildcardCheck(BaseResourceCheck):
    def __init__(self):
        name = "Ensure IAM policies do not grant wildcard permissions on all resources"
        id = "CKV_CUSTOM_1"
        supported_resources = [
            "aws_iam_role_policy",
            "aws_iam_user_policy", 
            "aws_iam_group_policy",
            "aws_iam_policy"
        ]
        categories = ["IAM"]
        guideline = """
        IAM policies should use specific permissions instead of wildcards (*).
        Wildcard permissions on all resources (*:* on *) create security risks.
        
        Bad example:
        {
            "Effect": "Allow",
            "Action": "*",
            "Resource": "*"
        }
        
        Good example:
        {
            "Effect": "Allow", 
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::my-bucket/*"
        }
        """
        
        super().__init__(
            name=name,
            id=id,
            categories=categories,
            supported_resources=supported_resources,
            guideline=guideline
        )

    def scan_resource_conf(self, conf):
        """
        Scan IAM policy for dangerous wildcard permissions
        :param conf: IAM policy configuration
        :return: CheckResult
        """
        # Get policy document
        policy_doc = self._get_policy_document(conf)
        if not policy_doc:
            return CheckResult.UNKNOWN
        
        # Parse policy document
        try:
            if isinstance(policy_doc, str):
                policy = json.loads(policy_doc)
            else:
                policy = policy_doc
        except (json.JSONDecodeError, TypeError):
            return CheckResult.UNKNOWN
        
        # Check for dangerous wildcard permissions
        if self._has_dangerous_wildcard_permissions(policy):
            return CheckResult.FAILED
            
        return CheckResult.PASSED
    
    def _get_policy_document(self, conf):
        """Extract policy document from configuration"""
        if "policy" in conf:
            return conf["policy"][0]
        return None
    
    def _has_dangerous_wildcard_permissions(self, policy):
        """
        Check if policy contains dangerous wildcard permissions
        Dangerous = Action: "*" AND Resource: "*"
        """
        statements = policy.get("Statement", [])
        if not isinstance(statements, list):
            statements = [statements]
        
        for statement in statements:
            # Only check Allow statements
            if statement.get("Effect") != "Allow":
                continue
            
            # Check if both Action and Resource are wildcards
            if self._has_wildcard_action(statement) and self._has_wildcard_resource(statement):
                return True
        
        return False
    
    def _has_wildcard_action(self, statement):
        """Check if statement has wildcard actions"""
        actions = statement.get("Action", [])
        if not isinstance(actions, list):
            actions = [actions]
        
        for action in actions:
            if action == "*":
                return True
        
        return False
    
    def _has_wildcard_resource(self, statement):
        """Check if statement has wildcard resources"""
        resources = statement.get("Resource", [])
        if not isinstance(resources, list):
            resources = [resources]
        
        for resource in resources:
            if resource == "*":
                return True
        
        return False

# Register the check
check = IAMWildcardCheck()
