"""
Custom Checkov Policies Package

This package contains custom Python-based Checkov policies for organization-specific
security and compliance requirements.

Directory Structure:
- iam/: Identity and Access Management policies
- networking/: Network security policies  
- database/: Database configuration policies
- storage/: Storage security policies

Usage:
    checkov -d . --external-checks-dir ./custom_checks/

The checks are automatically registered when the modules are imported.
"""

# Import all checks to register them
from .iam.iam_wildcard_check import check as iam_wildcard_check
from .networking.ec2_security_group_check import check as ec2_sg_check
from .database.rds_environment_check import check as rds_env_check

# List of all registered checks
REGISTERED_CHECKS = [
    iam_wildcard_check,
    ec2_sg_check,
    rds_env_check
]

__version__ = "1.0.0"
__author__ = "DevOps Team"
