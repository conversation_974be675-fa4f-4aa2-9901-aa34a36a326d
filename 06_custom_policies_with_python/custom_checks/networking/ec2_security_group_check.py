"""
Custom Checkov check to ensure EC2 instances have security groups attached
This check validates that EC2 instances are not using the default security group
"""

from checkov.terraform.checks.resource.base_resource_check import BaseResourceCheck
from checkov.common.models.enums import TrueOrFalse

class EC2SecurityGroupCheck(BaseResourceCheck):
    def __init__(self):
        name = "Ensure EC2 instances have explicit security groups attached"
        id = "CKV_CUSTOM_2"
        supported_resources = ["aws_instance"]
        categories = ["NETWORKING"]
        guideline = """
        EC2 instances should have explicit security groups attached rather than
        relying on the default security group. This ensures proper network
        access control and follows security best practices.
        
        Good examples:
        - vpc_security_group_ids = [aws_security_group.web.id]
        - security_groups = ["sg-12345678"]
        
        Bad example:
        - No security group specified (uses default)
        """
        
        super().__init__(
            name=name,
            id=id,
            categories=categories,
            supported_resources=supported_resources,
            guideline=guideline
        )

    def scan_resource_conf(self, conf):
        """
        Check if EC2 instance has explicit security groups
        :param conf: aws_instance configuration
        :return: CheckResult
        """
        # Check for vpc_security_group_ids (VPC instances)
        if "vpc_security_group_ids" in conf:
            sg_ids = conf["vpc_security_group_ids"][0]
            if self._is_valid_security_group_list(sg_ids):
                return CheckResult.PASSED
        
        # Check for security_groups (EC2-Classic instances)
        if "security_groups" in conf:
            sgs = conf["security_groups"][0]
            if self._is_valid_security_group_list(sgs):
                return CheckResult.PASSED
        
        # If no security groups are explicitly defined, it fails
        return CheckResult.FAILED
    
    def _is_valid_security_group_list(self, sg_list):
        """
        Check if security group list is valid (non-empty and not just default)
        :param sg_list: List of security groups or single security group
        :return: Boolean
        """
        if not sg_list:
            return False
        
        # Handle single security group (not in list)
        if isinstance(sg_list, str):
            sg_list = [sg_list]
        
        # Must be a list with at least one element
        if not isinstance(sg_list, list) or len(sg_list) == 0:
            return False
        
        # Check if any security group is explicitly defined
        # (not just "default" which might be implicit)
        for sg in sg_list:
            if isinstance(sg, str) and sg.strip():
                # If it's not just "default", it's probably explicit
                if sg.strip().lower() != "default":
                    return True
                # Even "default" is better than no security group
                return True
        
        return False

# Register the check
check = EC2SecurityGroupCheck()
