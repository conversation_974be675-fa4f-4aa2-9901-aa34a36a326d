# Test cases for RDS environment-specific configuration check
# This file contains RDS instances with different environment configurations

# COMPLIANT: Production RDS with all required settings
resource "aws_db_instance" "compliant_production" {
  identifier = "prod-database"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.medium"
  
  allocated_storage = 100
  storage_type      = "gp2"
  storage_encrypted = true          # Required for prod
  
  db_name  = "proddb"
  username = "admin"
  password = "secure-password-123"
  
  multi_az               = true     # Required for prod
  backup_retention_period = 14     # >= 7 days required for prod
  deletion_protection    = true     # Required for prod
  
  skip_final_snapshot = false
  
  tags = {
    Environment = "prod"            # Production environment
    Owner       = "database-team"
    Project     = "main-app"
  }
}

# NON-COMPLIANT: Production RDS missing required settings
resource "aws_db_instance" "non_compliant_production" {
  identifier = "prod-database-bad"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  storage_encrypted = false         # FAIL: Should be true for prod
  
  db_name  = "proddb"
  username = "admin"
  password = "password123"
  
  multi_az               = false    # FAIL: Should be true for prod
  backup_retention_period = 3      # FAIL: Should be >= 7 for prod
  deletion_protection    = false   # FAIL: Should be true for prod
  
  skip_final_snapshot = true
  
  tags = {
    Environment = "prod"            # Production environment
    Owner       = "developer"
    Project     = "test-app"
  }
}

# COMPLIANT: Staging RDS with required settings
resource "aws_db_instance" "compliant_staging" {
  identifier = "staging-database"
  
  engine         = "postgres"
  engine_version = "13.7"
  instance_class = "db.t3.small"
  
  allocated_storage = 50
  storage_type      = "gp2"
  storage_encrypted = true          # Required for staging
  
  db_name  = "stagingdb"
  username = "admin"
  password = "staging-password-123"
  
  multi_az               = false    # Not required for staging
  backup_retention_period = 5      # >= 3 days required for staging
  deletion_protection    = false   # Not required for staging
  
  skip_final_snapshot = true
  
  tags = {
    Environment = "staging"         # Staging environment
    Owner       = "qa-team"
    Project     = "main-app"
  }
}

# NON-COMPLIANT: Staging RDS missing encryption
resource "aws_db_instance" "non_compliant_staging" {
  identifier = "staging-database-bad"
  
  engine         = "postgres"
  engine_version = "13.7"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  storage_encrypted = false         # FAIL: Should be true for staging
  
  db_name  = "stagingdb"
  username = "admin"
  password = "password123"
  
  multi_az               = false
  backup_retention_period = 1      # FAIL: Should be >= 3 for staging
  deletion_protection    = false
  
  skip_final_snapshot = true
  
  tags = {
    Environment = "staging"         # Staging environment
    Owner       = "developer"
    Project     = "test-app"
  }
}

# COMPLIANT: Development RDS with minimal requirements
resource "aws_db_instance" "compliant_development" {
  identifier = "dev-database"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  storage_encrypted = false         # Not required for dev
  
  db_name  = "devdb"
  username = "admin"
  password = "dev-password"
  
  multi_az               = false    # Not required for dev
  backup_retention_period = 2      # >= 1 day required for dev
  deletion_protection    = false   # Not required for dev
  
  skip_final_snapshot = true
  
  tags = {
    Environment = "dev"             # Development environment
    Owner       = "developer"
    Project     = "test-app"
  }
}

# NON-COMPLIANT: Development RDS with insufficient backup retention
resource "aws_db_instance" "non_compliant_development" {
  identifier = "dev-database-bad"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  storage_encrypted = false
  
  db_name  = "devdb"
  username = "admin"
  password = "password123"
  
  multi_az               = false
  backup_retention_period = 0      # FAIL: Should be >= 1 for dev
  deletion_protection    = false
  
  skip_final_snapshot = true
  
  tags = {
    Environment = "dev"             # Development environment
    Owner       = "developer"
    Project     = "test-app"
  }
}

# SKIPPED: RDS without environment tag (check will be skipped)
resource "aws_db_instance" "no_environment_tag" {
  identifier = "no-env-database"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  storage_encrypted = false
  
  db_name  = "testdb"
  username = "admin"
  password = "password123"
  
  multi_az               = false
  backup_retention_period = 0
  deletion_protection    = false
  
  skip_final_snapshot = true
  
  tags = {
    # No Environment tag - check will be skipped
    Owner   = "developer"
    Project = "test-app"
  }
}

# SKIPPED: RDS with unknown environment value
resource "aws_db_instance" "unknown_environment" {
  identifier = "unknown-env-database"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  storage_encrypted = false
  
  db_name  = "testdb"
  username = "admin"
  password = "password123"
  
  multi_az               = false
  backup_retention_period = 0
  deletion_protection    = false
  
  skip_final_snapshot = true
  
  tags = {
    Environment = "testing"         # Unknown environment - check will be skipped
    Owner       = "developer"
    Project     = "test-app"
  }
}

# COMPLIANT: Alternative environment tag names
resource "aws_db_instance" "alternative_env_tag" {
  identifier = "alt-env-database"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  storage_encrypted = false
  
  db_name  = "testdb"
  username = "admin"
  password = "password123"
  
  multi_az               = false
  backup_retention_period = 2      # >= 1 day for dev
  deletion_protection    = false
  
  skip_final_snapshot = true
  
  tags = {
    Env     = "development"         # Alternative tag name
    Owner   = "developer"
    Project = "test-app"
  }
}
