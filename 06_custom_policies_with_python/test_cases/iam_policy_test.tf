# Test cases for IAM wildcard policy check
# This file contains both compliant and non-compliant IAM policies

# NON-COMPLIANT: Dangerous wildcard policy (Action: "*", Resource: "*")
resource "aws_iam_role_policy" "dangerous_wildcard" {
  name = "dangerous-wildcard-policy"
  role = aws_iam_role.test_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "*"          # Wildcard action
        Resource = "*"        # Wildcard resource - This should FAIL
      }
    ]
  })
}

# COMPLIANT: Specific permissions
resource "aws_iam_role_policy" "specific_permissions" {
  name = "specific-permissions-policy"
  role = aws_iam_role.test_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject"
        ]
        Resource = "arn:aws:s3:::my-bucket/*"
      }
    ]
  })
}

# COMPLIANT: Wildcard action but specific resource
resource "aws_iam_role_policy" "wildcard_action_specific_resource" {
  name = "wildcard-action-specific-resource"
  role = aws_iam_role.test_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "s3:*"       # Wildcard action
        Resource = [          # But specific resources
          "arn:aws:s3:::my-bucket",
          "arn:aws:s3:::my-bucket/*"
        ]
      }
    ]
  })
}

# COMPLIANT: Specific action but wildcard resource (less dangerous)
resource "aws_iam_role_policy" "specific_action_wildcard_resource" {
  name = "specific-action-wildcard-resource"
  role = aws_iam_role.test_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "s3:ListBucket"  # Specific action
        Resource = "*"            # Wildcard resource (but limited action)
      }
    ]
  })
}

# NON-COMPLIANT: Multiple statements with one dangerous wildcard
resource "aws_iam_role_policy" "mixed_permissions" {
  name = "mixed-permissions-policy"
  role = aws_iam_role.test_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "s3:GetObject"
        Resource = "arn:aws:s3:::my-bucket/*"
      },
      {
        Effect = "Allow"
        Action = "*"          # Dangerous wildcard
        Resource = "*"        # This should FAIL the entire policy
      }
    ]
  })
}

# COMPLIANT: Deny statement with wildcards (deny is safer than allow)
resource "aws_iam_role_policy" "deny_wildcard" {
  name = "deny-wildcard-policy"
  role = aws_iam_role.test_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "s3:*"
        Resource = "arn:aws:s3:::my-bucket/*"
      },
      {
        Effect = "Deny"       # Deny statements are OK with wildcards
        Action = "*"
        Resource = "*"
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}

# Test IAM role for the policies
resource "aws_iam_role" "test_role" {
  name = "test-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Environment = "test"
    Purpose     = "checkov-testing"
  }
}

# Test standalone IAM policy
resource "aws_iam_policy" "standalone_dangerous" {
  name        = "standalone-dangerous-policy"
  description = "Standalone policy with dangerous permissions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "*"          # This should FAIL
        Resource = "*"
      }
    ]
  })
}

# Test user policy
resource "aws_iam_user" "test_user" {
  name = "test-user"
  
  tags = {
    Environment = "test"
  }
}

resource "aws_iam_user_policy" "user_specific_policy" {
  name = "user-specific-policy"
  user = aws_iam_user.test_user.name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iam:GetUser",
          "iam:ChangePassword"
        ]
        Resource = "arn:aws:iam::*:user/$${aws:username}"
      }
    ]
  })
}
