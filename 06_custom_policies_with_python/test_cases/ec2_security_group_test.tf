# Test cases for EC2 security group check
# This file contains both compliant and non-compliant EC2 instances

# COMPLIANT: EC2 instance with explicit VPC security group
resource "aws_instance" "compliant_vpc_instance" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  vpc_security_group_ids = [aws_security_group.web_sg.id]
  
  tags = {
    Name        = "compliant-vpc-instance"
    Environment = "test"
  }
}

# COMPLIANT: EC2 instance with multiple security groups
resource "aws_instance" "compliant_multiple_sgs" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  vpc_security_group_ids = [
    aws_security_group.web_sg.id,
    aws_security_group.app_sg.id
  ]
  
  tags = {
    Name        = "compliant-multiple-sgs"
    Environment = "test"
  }
}

# COMPLIANT: EC2-Classic instance with security groups
resource "aws_instance" "compliant_classic_instance" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  security_groups = ["web-sg", "app-sg"]
  
  tags = {
    Name        = "compliant-classic-instance"
    Environment = "test"
  }
}

# NON-COMPLIANT: EC2 instance without any security groups
resource "aws_instance" "non_compliant_no_sg" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  # No security groups specified - will use default
  
  tags = {
    Name        = "non-compliant-no-sg"
    Environment = "test"
  }
}

# NON-COMPLIANT: EC2 instance with empty security group list
resource "aws_instance" "non_compliant_empty_sg_list" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  vpc_security_group_ids = []  # Empty list
  
  tags = {
    Name        = "non-compliant-empty-sg-list"
    Environment = "test"
  }
}

# COMPLIANT: EC2 instance with hardcoded security group ID
resource "aws_instance" "compliant_hardcoded_sg" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  vpc_security_group_ids = ["sg-12345678"]  # Hardcoded SG ID
  
  tags = {
    Name        = "compliant-hardcoded-sg"
    Environment = "test"
  }
}

# COMPLIANT: EC2 instance with default security group explicitly specified
resource "aws_instance" "compliant_explicit_default" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  security_groups = ["default"]  # Explicit default is better than implicit
  
  tags = {
    Name        = "compliant-explicit-default"
    Environment = "test"
  }
}

# Supporting security groups for testing
resource "aws_security_group" "web_sg" {
  name_prefix = "web-sg-"
  description = "Security group for web servers"
  
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/16"]
  }
  
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/16"]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = {
    Name        = "web-sg"
    Environment = "test"
  }
}

resource "aws_security_group" "app_sg" {
  name_prefix = "app-sg-"
  description = "Security group for application servers"
  
  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/16"]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = {
    Name        = "app-sg"
    Environment = "test"
  }
}

# Test with launch template (should also be compliant if SG is specified)
resource "aws_launch_template" "compliant_template" {
  name_prefix   = "compliant-template-"
  image_id      = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  vpc_security_group_ids = [aws_security_group.web_sg.id]
  
  tag_specifications {
    resource_type = "instance"
    tags = {
      Name        = "template-instance"
      Environment = "test"
    }
  }
}

resource "aws_instance" "from_template" {
  launch_template {
    id      = aws_launch_template.compliant_template.id
    version = "$Latest"
  }
  
  tags = {
    Name        = "from-template"
    Environment = "test"
  }
}
