# 02 - First <PERSON><PERSON> and <PERSON><PERSON><PERSON> Basics

## 🎯 Learning Objectives
- Perform your first Checkov scan on a Terraform AWS project
- Understand and interpret Checkov output
- Learn essential CLI flags and options
- Filter checks by severity, categories, and frameworks
- Use skip annotations to ignore specific checks

## 🚀 Your First Checkov Scan

Let's start with a practical example using a sample AWS Terraform project.

### Step 1: Create Sample Terraform Code

Create a directory structure for our first scan:

```bash
mkdir my-first-scan && cd my-first-scan
```

Now create the following Terraform files:

**main.tf**
```hcl
# Provider configuration
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "us-west-2"
}

# S3 Bucket - Intentionally insecure for learning
resource "aws_s3_bucket" "example" {
  bucket = "my-example-bucket-12345"
}

resource "aws_s3_bucket_public_access_block" "example" {
  bucket = aws_s3_bucket.example.id

  block_public_acls       = false  # Insecure
  block_public_policy     = false  # Insecure
  ignore_public_acls      = false  # Insecure
  restrict_public_buckets = false  # Insecure
}

# EC2 Instance - Insecure configuration
resource "aws_instance" "web" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t2.micro"
  
  # No security group specified - will use default
  # No encryption specified
  
  tags = {
    Name = "WebServer"
  }
}

# Security Group - Overly permissive
resource "aws_security_group" "web" {
  name_prefix = "web-"
  
  ingress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # Insecure - allows all traffic
  }
  
  egress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
```

### Step 2: Run Your First Scan

```bash
# Basic scan
checkov -f main.tf

# Scan entire directory
checkov -d .

# Scan with more verbose output
checkov -d . --compact
```

## 📊 Understanding Checkov Output

When you run the scan, you'll see output like this:

```
       _               _              
   ___| |__   ___  ___| | _______   __
  / __| '_ \ / _ \/ __| |/ / _ \ \ / /
 | (__| | | |  __/ (__|   < (_) \ V / 
  \___|_| |_|\___|\___|_|\_\___/ \_/  
                                      
By bridgecrew.io | version: 2.4.0

terraform scan results:

Passed checks: 15, Failed checks: 8, Skipped checks: 0

Check: CKV_AWS_18: "Ensure the S3 bucket has access logging configured"
	FAILED for resource: aws_s3_bucket.example
	File: /main.tf:15-17
	Guide: https://docs.bridgecrew.io/docs/s3_13-enable-logging

		15 | resource "aws_s3_bucket" "example" {
		16 |   bucket = "my-example-bucket-12345"
		17 | }

Check: CKV_AWS_21: "Ensure S3 bucket has MFA delete enabled"
	FAILED for resource: aws_s3_bucket.example
	File: /main.tf:15-17
```

### Output Components Explained:

1. **Header**: Shows Checkov version and scan type
2. **Summary**: Total passed, failed, and skipped checks
3. **Failed Checks**: Detailed information about each failure
4. **Check ID**: Unique identifier (e.g., CKV_AWS_18)
5. **Description**: What the check validates
6. **Resource**: Which resource failed the check
7. **File Location**: Exact line numbers
8. **Guide Link**: Documentation for remediation

## 🔧 Essential CLI Flags and Options

### Basic Scanning Options

```bash
# Scan single file
checkov -f main.tf

# Scan directory
checkov -d /path/to/terraform

# Scan with specific framework
checkov -d . --framework terraform

# Recursive directory scan
checkov -d . --recursive

# Scan external modules
checkov -d . --download-external-modules true
```

### Output Control

```bash
# Compact output (less verbose)
checkov -d . --compact

# Quiet mode (only show failures)
checkov -d . --quiet

# Very quiet (minimal output)
checkov -d . -q

# Show only passed checks
checkov -d . --check-passed

# Show only failed checks  
checkov -d . --check-failed
```

### Filtering Options

```bash
# Run specific checks only
checkov -d . --check CKV_AWS_18,CKV_AWS_21

# Skip specific checks
checkov -d . --skip-check CKV_AWS_18,CKV_AWS_21

# Filter by severity
checkov -d . --severity HIGH,CRITICAL

# Filter by category
checkov -d . --category encryption,logging
```

## 🎯 Filtering Checks by Severity, Categories, and Frameworks

### 1. Severity Filtering

Checkov assigns severity levels to checks:

```bash
# Show only critical and high severity issues
checkov -d . --severity CRITICAL,HIGH

# Available severity levels:
# - CRITICAL
# - HIGH  
# - MEDIUM
# - LOW
# - INFO
```

### 2. Category Filtering

Group checks by security categories:

```bash
# Focus on encryption issues
checkov -d . --category encryption

# Multiple categories
checkov -d . --category encryption,logging,networking

# Available categories include:
# - encryption
# - logging  
# - networking
# - iam
# - backup
# - monitoring
```

### 3. Framework Filtering

```bash
# Terraform only
checkov -d . --framework terraform

# Multiple frameworks
checkov -d . --framework terraform,dockerfile

# Available frameworks:
# - terraform
# - cloudformation
# - kubernetes
# - dockerfile
# - serverless
```

### 4. Compliance Framework Filtering

```bash
# CIS benchmarks only
checkov -d . --framework terraform --check-compliance cis

# NIST compliance
checkov -d . --check-compliance nist

# Multiple compliance frameworks
checkov -d . --check-compliance cis,nist,soc2
```

## 🚫 Skip Annotations - Ignoring Specific Checks

Sometimes you need to skip certain checks for valid business reasons. Checkov provides several ways to do this.

### 1. Inline Skip Annotations

**Single Check Skip:**
```hcl
resource "aws_s3_bucket" "example" {
  #checkov:skip=CKV_AWS_18:Logging not required for this test bucket
  bucket = "my-example-bucket-12345"
}
```

**Multiple Checks Skip:**
```hcl
resource "aws_s3_bucket" "example" {
  #checkov:skip=CKV_AWS_18:Logging not required for this test bucket
  #checkov:skip=CKV_AWS_21:MFA delete not needed in dev environment
  bucket = "my-example-bucket-12345"
}
```

**Block-level Skip:**
```hcl
#checkov:skip=CKV_AWS_79:Security group is for internal testing only
resource "aws_security_group" "web" {
  name_prefix = "web-"
  
  ingress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
```

### 2. File-level Skip

Create a `.checkov.yml` configuration file:

```yaml
# .checkov.yml
skip-check:
  - CKV_AWS_18  # S3 access logging
  - CKV_AWS_21  # S3 MFA delete

# Skip for specific files
skip-path:
  - "test/*.tf"
  - "examples/"
```

### 3. Directory-level Skip

```bash
# Skip checks for entire directory
checkov -d . --skip-path test/,examples/

# Skip specific checks globally
checkov -d . --skip-check CKV_AWS_18,CKV_AWS_21
```

## 🧪 Practical Exercise

Let's practice with our sample code:

### Exercise 1: Basic Scan Analysis
1. Run `checkov -f main.tf`
2. Count how many checks failed
3. Identify the top 3 most critical issues

### Exercise 2: Filtering Practice
```bash
# Try these commands and observe the differences:
checkov -f main.tf --severity HIGH
checkov -f main.tf --category encryption
checkov -f main.tf --compact
checkov -f main.tf --quiet
```

### Exercise 3: Skip Annotations
1. Add skip annotations to suppress S3 logging warnings
2. Re-run the scan and verify the warnings are gone
3. Add a comment explaining why the check was skipped

## 📈 Advanced CLI Examples

### Complex Filtering
```bash
# High severity encryption issues only
checkov -d . --severity HIGH --category encryption --compact

# Terraform files with CIS compliance, excluding test directories
checkov -d . --framework terraform --check-compliance cis --skip-path test/

# Scan with external modules and custom output
checkov -d . --download-external-modules true --output json > scan-results.json
```

### Integration with Other Tools
```bash
# Pipe results to jq for JSON processing
checkov -d . --output json | jq '.results.failed_checks | length'

# Save results to file
checkov -d . --output json --output-file-path results.json

# Generate HTML report
checkov -d . --output html --output-file-path report.html
```

## 🎯 Next Steps

You now know how to:
- ✅ Run basic Checkov scans
- ✅ Interpret scan results
- ✅ Filter checks by various criteria
- ✅ Skip checks when necessary

**Next:** [03 - Scan Terraform Modules and Dependencies](../03_scan_terraform_modules_and_dependencies/) →

## 📚 Quick Reference

### Most Used Commands
```bash
# Basic scan
checkov -d .

# Compact output with high severity only
checkov -d . --compact --severity HIGH

# Skip specific checks
checkov -d . --skip-check CKV_AWS_18,CKV_AWS_21

# JSON output for automation
checkov -d . --output json
```

### Common Skip Patterns
```hcl
#checkov:skip=CKV_AWS_18:Business justification here
#checkov:skip=CKV_AWS_21:Another reason
```

---

**🎉 Great job!** You've mastered the basics of running Checkov scans and understanding the output. You're ready to dive deeper into scanning complex Terraform projects with modules.
