# Example showing how to use skip annotations
# This file demonstrates various ways to skip Checkov checks

# Provider configuration
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "us-west-2"
}

# S3 Bucket with skip annotations
resource "aws_s3_bucket" "example" {
  #checkov:skip=CKV_AWS_18:Access logging not required for this test bucket
  #checkov:skip=CKV_AWS_21:MFA delete not needed in development environment
  bucket = "my-example-bucket-12345"
}

resource "aws_s3_bucket_public_access_block" "example" {
  bucket = aws_s3_bucket.example.id

  block_public_acls       = false  # Insecure but intentional for demo
  block_public_policy     = false  # Insecure but intentional for demo
  ignore_public_acls      = false  # Insecure but intentional for demo
  restrict_public_buckets = false  # Insecure but intentional for demo
}

# Security Group with business justification for skip
#checkov:skip=CKV_AWS_79:This security group is used for internal testing and requires broad access
resource "aws_security_group" "web" {
  name_prefix = "web-"
  
  ingress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # Normally insecure, but skipped for testing
  }
  
  egress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# EC2 Instance with multiple skips
resource "aws_instance" "web" {
  #checkov:skip=CKV_AWS_79:Security group association handled separately
  #checkov:skip=CKV_AWS_8:EBS encryption not required for this demo instance
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t2.micro"
  
  tags = {
    Name = "WebServer"
  }
}

# RDS Instance with skip for development environment
resource "aws_db_instance" "example" {
  #checkov:skip=CKV_AWS_16:Database encryption not required in development
  #checkov:skip=CKV_AWS_17:Database backup not required for temporary dev database
  identifier = "example-db"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  
  db_name  = "exampledb"
  username = "admin"
  password = "password123"  # Still insecure - should use AWS Secrets Manager
  
  skip_final_snapshot = true
  
  tags = {
    Name        = "ExampleDB"
    Environment = "Development"
  }
}

# Lambda function with environment-specific skips
resource "aws_lambda_function" "example" {
  #checkov:skip=CKV_AWS_50:X-Ray tracing not required for this simple function
  #checkov:skip=CKV_AWS_115:Reserved concurrency not needed for low-traffic function
  #checkov:skip=CKV_AWS_116:Dead letter queue not required for this demo function
  filename         = "lambda.zip"
  function_name    = "example-lambda"
  role            = aws_iam_role.lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  
  environment {
    variables = {
      ENVIRONMENT = "development"
    }
  }
}

# IAM Role for Lambda (more restrictive than previous example)
resource "aws_iam_role" "lambda_role" {
  name = "lambda-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# Attach basic Lambda execution policy
resource "aws_iam_role_policy_attachment" "lambda_basic" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}
