# Provider configuration
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "us-west-2"
}

# S3 Bucket - Intentionally insecure for learning
resource "aws_s3_bucket" "example" {
  bucket = "my-example-bucket-12345"
}

resource "aws_s3_bucket_public_access_block" "example" {
  bucket = aws_s3_bucket.example.id

  block_public_acls       = false  # Insecure
  block_public_policy     = false  # Insecure
  ignore_public_acls      = false  # Insecure
  restrict_public_buckets = false  # Insecure
}

# EC2 Instance - Insecure configuration
resource "aws_instance" "web" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t2.micro"
  
  # No security group specified - will use default
  # No encryption specified
  
  tags = {
    Name = "WebServer"
  }
}

# Security Group - Overly permissive
resource "aws_security_group" "web" {
  name_prefix = "web-"
  
  ingress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # Insecure - allows all traffic
  }
  
  egress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# RDS Instance - Multiple security issues
resource "aws_db_instance" "example" {
  identifier = "example-db"
  
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.micro"
  
  allocated_storage = 20
  storage_type      = "gp2"
  # storage_encrypted = false  # Insecure - not encrypted
  
  db_name  = "exampledb"
  username = "admin"
  password = "password123"  # Insecure - hardcoded password
  
  # publicly_accessible = true  # Insecure - publicly accessible
  skip_final_snapshot = true
  
  tags = {
    Name = "ExampleDB"
  }
}

# IAM Role with overly broad permissions
resource "aws_iam_role" "example" {
  name = "example-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "example" {
  name = "example-policy"
  role = aws_iam_role.example.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "*"  # Insecure - wildcard permissions
        Resource = "*"
      }
    ]
  })
}

# Lambda function without proper configuration
resource "aws_lambda_function" "example" {
  filename         = "lambda.zip"
  function_name    = "example-lambda"
  role            = aws_iam_role.example.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  
  # No environment variable encryption
  # No VPC configuration
  # No dead letter queue
  
  environment {
    variables = {
      DATABASE_PASSWORD = "hardcoded-password"  # Insecure
    }
  }
}
