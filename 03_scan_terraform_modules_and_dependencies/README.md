# 03 - Scan Terraform Modules and Dependencies

## 🎯 Learning Objectives
- Scan Terraform projects with local and remote modules
- Understand <PERSON><PERSON>'s graph-based analysis capabilities
- Visualize resource relationships and dependencies
- Troubleshoot common module scanning issues
- Configure external module downloading and caching

## 🏗️ Understanding Terraform Modules in Checkov Context

Terraform modules add complexity to security scanning because:
- **Dependencies**: Resources in modules may depend on parent configurations
- **Variable Passing**: Security configurations might be passed as variables
- **Remote Sources**: Modules from Git, Terraform Registry, or other sources
- **Graph Relationships**: Cross-module resource relationships

## 📦 Types of Modules Checkov Can Scan

### 1. Local Modules
```
project/
├── main.tf
├── modules/
│   ├── vpc/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   └── ec2/
│       ├── main.tf
│       ├── variables.tf
│       └── outputs.tf
```

### 2. Remote Modules
- **Terraform Registry**: `terraform-aws-modules/vpc/aws`
- **Git Repositories**: `git::https://github.com/user/repo.git`
- **HTTP URLs**: Direct module downloads
- **Private Registries**: Enterprise module repositories

## 🔍 Scanning Local Modules

Let's create a practical example with local modules:

### Project Structure
```bash
mkdir terraform-modules-example && cd terraform-modules-example
mkdir -p modules/{vpc,security,compute}
```

### Root Configuration (main.tf)
```hcl
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC Module
module "vpc" {
  source = "./modules/vpc"
  
  vpc_cidr             = var.vpc_cidr
  availability_zones   = var.availability_zones
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = var.common_tags
}

# Security Module
module "security" {
  source = "./modules/security"
  
  vpc_id = module.vpc.vpc_id
  
  allowed_cidr_blocks = var.allowed_cidr_blocks
  
  tags = var.common_tags
}

# Compute Module
module "compute" {
  source = "./modules/compute"
  
  vpc_id            = module.vpc.vpc_id
  subnet_ids        = module.vpc.private_subnet_ids
  security_group_id = module.security.web_security_group_id
  
  instance_type = var.instance_type
  key_name      = var.key_name
  
  tags = var.common_tags
}
```

### VPC Module (modules/vpc/main.tf)
```hcl
# VPC
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support

  tags = merge(var.tags, {
    Name = "main-vpc"
  })
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = merge(var.tags, {
    Name = "main-igw"
  })
}

# Public Subnets
resource "aws_subnet" "public" {
  count = length(var.availability_zones)

  vpc_id            = aws_vpc.main.id
  cidr_block        = cidrsubnet(var.vpc_cidr, 8, count.index)
  availability_zone = var.availability_zones[count.index]

  map_public_ip_on_launch = true  # Potential security issue

  tags = merge(var.tags, {
    Name = "public-subnet-${count.index + 1}"
    Type = "Public"
  })
}

# Private Subnets
resource "aws_subnet" "private" {
  count = length(var.availability_zones)

  vpc_id            = aws_vpc.main.id
  cidr_block        = cidrsubnet(var.vpc_cidr, 8, count.index + 10)
  availability_zone = var.availability_zones[count.index]

  tags = merge(var.tags, {
    Name = "private-subnet-${count.index + 1}"
    Type = "Private"
  })
}

# Route Table for Public Subnets
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main.id
  }

  tags = merge(var.tags, {
    Name = "public-rt"
  })
}

# Route Table Associations
resource "aws_route_table_association" "public" {
  count = length(aws_subnet.public)

  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}
```

## 🌐 Scanning Remote Modules

### Configuration with Remote Modules
```hcl
# Using Terraform Registry modules
module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "~> 5.0"

  name = "my-vpc"
  cidr = "10.0.0.0/16"

  azs             = ["us-west-2a", "us-west-2b", "us-west-2c"]
  private_subnets = ["********/24", "********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24", "**********/24"]

  enable_nat_gateway = true
  enable_vpn_gateway = true

  tags = {
    Terraform   = "true"
    Environment = "dev"
  }
}

# Using Git repository module
module "security_group" {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-security-group.git?ref=v4.17.1"

  name        = "web-server"
  description = "Security group for web-server"
  vpc_id      = module.vpc.vpc_id

  ingress_cidr_blocks = ["0.0.0.0/0"]  # Potential security issue
  ingress_rules       = ["http-80-tcp", "https-443-tcp"]
  egress_rules        = ["all-all"]
}
```

### Scanning Commands for Remote Modules

```bash
# Download external modules before scanning
checkov -d . --download-external-modules true

# Scan with module caching
checkov -d . --download-external-modules true --external-modules-download-path ./.checkov_modules

# Force re-download of modules
checkov -d . --download-external-modules true --force-download-external-modules
```

## 🕸️ Graph-Based Analysis Deep Dive

Checkov builds a resource dependency graph to understand relationships:

### Example: Cross-Module Dependencies
```hcl
# main.tf
module "database" {
  source = "./modules/rds"
  
  vpc_id            = module.vpc.vpc_id
  subnet_ids        = module.vpc.private_subnet_ids
  security_group_id = module.security.database_sg_id
}

module "application" {
  source = "./modules/app"
  
  database_endpoint = module.database.endpoint
  vpc_id           = module.vpc.vpc_id
}
```

Checkov understands that:
1. Database depends on VPC and Security modules
2. Application depends on Database module
3. Security configurations flow through the dependency chain

### Graph Analysis Commands
```bash
# Generate dependency graph
checkov -d . --create-graph

# Output graph in DOT format
checkov -d . --create-graph --graph-output dot

# Save graph to file
checkov -d . --create-graph --graph-output-file-path dependency-graph.dot
```

## 📊 Visualizing Resource Relationships

### Using Graphviz to Visualize Dependencies
```bash
# Install Graphviz (macOS)
brew install graphviz

# Install Graphviz (Ubuntu)
sudo apt-get install graphviz

# Generate and visualize graph
checkov -d . --create-graph --graph-output-file-path graph.dot
dot -Tpng graph.dot -o dependency-graph.png
```

### Example Graph Visualization
The generated graph shows:
- **Nodes**: Resources and modules
- **Edges**: Dependencies and relationships
- **Colors**: Different resource types
- **Labels**: Resource names and types

## 🔧 Advanced Module Scanning Techniques

### 1. Module-Specific Configuration
```yaml
# .checkov.yml
framework:
  - terraform

download-external-modules: true
external-modules-download-path: ./.checkov_modules

# Skip checks for specific modules
skip-path:
  - "modules/legacy/"
  - ".checkov_modules/"

# Module-specific check configuration
check:
  - CKV_AWS_*  # Only AWS checks

skip-check:
  - CKV_TF_1   # Skip Terraform-specific checks in modules
```

### 2. Scanning Strategies for Large Projects
```bash
# Parallel scanning for better performance
checkov -d . --download-external-modules true --parallel

# Scan only changed modules (in CI/CD)
checkov -d . --download-external-modules true --baseline .checkov.baseline

# Deep module analysis
checkov -d . --download-external-modules true --deep-analysis
```

### 3. Module Variable Analysis
Checkov can trace security configurations through module variables:

```hcl
# Parent configuration
module "database" {
  source = "./modules/rds"
  
  encryption_enabled = false  # Checkov will flag this
  backup_retention   = 0      # Checkov will flag this
}

# Module configuration (modules/rds/main.tf)
resource "aws_db_instance" "main" {
  storage_encrypted   = var.encryption_enabled  # Traced from parent
  backup_retention_period = var.backup_retention # Traced from parent
}
```

## 🚨 Troubleshooting Module Scan Issues

### Common Issues and Solutions

#### 1. Module Download Failures
```bash
# Error: Failed to download module
# Solution: Check network connectivity and module source
checkov -d . --download-external-modules true --debug

# For private repositories, ensure SSH keys are configured
ssh-add ~/.ssh/id_rsa
checkov -d . --download-external-modules true
```

#### 2. Module Path Issues
```bash
# Error: Module not found
# Solution: Use absolute paths or verify relative paths
checkov -d . --download-external-modules true --external-modules-download-path $(pwd)/.modules
```

#### 3. Version Conflicts
```hcl
# Specify exact versions to avoid conflicts
module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "= 5.1.0"  # Exact version
}
```

#### 4. Memory Issues with Large Modules
```bash
# Increase memory limit for large module scans
export CHECKOV_MAX_MEMORY=4096
checkov -d . --download-external-modules true
```

### Debug Commands
```bash
# Enable debug logging
checkov -d . --download-external-modules true --debug

# Verbose output for troubleshooting
checkov -d . --download-external-modules true -v

# Check module download location
ls -la ./.checkov_modules/
```

## 🧪 Practical Exercise: Multi-Module Project

### Exercise Setup
1. Create the project structure shown above
2. Implement all three modules (VPC, Security, Compute)
3. Add remote module dependencies
4. Configure variables and outputs

### Scanning Tasks
```bash
# Task 1: Basic module scan
checkov -d .

# Task 2: Scan with external modules
checkov -d . --download-external-modules true

# Task 3: Generate dependency graph
checkov -d . --create-graph --graph-output-file-path project-graph.dot

# Task 4: Filter module-specific issues
checkov -d . --download-external-modules true --category networking

# Task 5: Skip module-specific checks
checkov -d . --skip-path "modules/legacy/"
```

## 📈 Performance Optimization for Module Scans

### 1. Caching Strategies
```bash
# Use persistent module cache
export CHECKOV_MODULES_CACHE_DIR=~/.checkov_modules
checkov -d . --download-external-modules true

# Reuse downloaded modules
checkov -d . --download-external-modules true --reuse-external-modules
```

### 2. Selective Scanning
```bash
# Scan only specific modules
checkov -f modules/vpc/main.tf

# Scan changed files only (in CI/CD)
git diff --name-only HEAD~1 | grep '\.tf$' | xargs checkov -f
```

## 🎯 Next Steps

You now understand how to:
- ✅ Scan projects with local and remote modules
- ✅ Understand module dependencies and relationships
- ✅ Troubleshoot common module scanning issues
- ✅ Optimize performance for large module projects

**Next:** [04 - Understanding Check IDs and Frameworks](../04_understanding_check_ids_and_frameworks/) →

## 📚 Quick Reference

### Essential Module Scanning Commands
```bash
# Basic module scan
checkov -d .

# With external modules
checkov -d . --download-external-modules true

# Generate dependency graph
checkov -d . --create-graph

# Debug module issues
checkov -d . --download-external-modules true --debug
```

### Module Configuration Best Practices
- Always specify module versions
- Use consistent variable naming
- Document module dependencies
- Test modules independently
- Cache external modules in CI/CD

---

**🎉 Excellent!** You've mastered scanning complex Terraform projects with modules. You understand how Checkov analyzes dependencies and can troubleshoot common issues.
