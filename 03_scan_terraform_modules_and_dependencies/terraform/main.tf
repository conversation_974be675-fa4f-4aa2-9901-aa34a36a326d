# Root configuration demonstrating module usage
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC Module - Local module
module "vpc" {
  source = "./modules/vpc"
  
  vpc_cidr             = var.vpc_cidr
  availability_zones   = var.availability_zones
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = var.common_tags
}

# Security Module - Local module
module "security" {
  source = "./modules/security"
  
  vpc_id = module.vpc.vpc_id
  
  allowed_cidr_blocks = var.allowed_cidr_blocks
  
  tags = var.common_tags
}

# Compute Module - Local module
module "compute" {
  source = "./modules/compute"
  
  vpc_id            = module.vpc.vpc_id
  subnet_ids        = module.vpc.private_subnet_ids
  security_group_id = module.security.web_security_group_id
  
  instance_type = var.instance_type
  key_name      = var.key_name
  
  tags = var.common_tags
}

# Remote module example - Terraform Registry
module "s3_bucket" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "~> 3.0"

  bucket = "my-example-bucket-${random_id.bucket_suffix.hex}"
  
  # Intentionally insecure configurations for learning
  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
  
  versioning = {
    enabled = false  # Should be true for security
  }
  
  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"  # Should use KMS
      }
    }
  }

  tags = var.common_tags
}

# Random ID for unique bucket naming
resource "random_id" "bucket_suffix" {
  byte_length = 4
}

# Remote module example - Git repository
module "security_group_web" {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-security-group.git?ref=v4.17.1"

  name        = "web-server-sg"
  description = "Security group for web server"
  vpc_id      = module.vpc.vpc_id

  # Intentionally permissive for learning
  ingress_cidr_blocks = ["0.0.0.0/0"]  # Should be more restrictive
  ingress_rules       = ["http-80-tcp", "https-443-tcp", "ssh-tcp"]
  egress_rules        = ["all-all"]

  tags = var.common_tags
}

# Database module with cross-module dependencies
module "database" {
  source = "./modules/database"
  
  vpc_id            = module.vpc.vpc_id
  subnet_ids        = module.vpc.private_subnet_ids
  security_group_id = module.security.database_security_group_id
  
  db_name     = var.db_name
  db_username = var.db_username
  
  # Security configurations passed as variables
  encryption_enabled = false  # Should be true
  backup_retention   = 0      # Should be > 0
  multi_az          = false   # Should be true for production
  
  tags = var.common_tags
}
