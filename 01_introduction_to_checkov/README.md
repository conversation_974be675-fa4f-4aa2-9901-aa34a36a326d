# 01 - Introduction to Checkov

## 🎯 Learning Objectives
- Understand what Checkov is and its role in Infrastructure as Code security
- Learn key features and benefits of using Checkov
- Identify when to use Check<PERSON> in the Terraform lifecycle
- Explore supported IaC tools and frameworks
- Install Checkov using different methods

## 📖 What is Checkov?

**Checkov** is a static code analysis tool for Infrastructure as Code (IaC) that scans cloud infrastructure configurations to find misconfigurations before they're deployed.

### Key Characteristics:
- **Open Source**: Developed by Bridgecrew (now part of Palo Alto Networks)
- **Multi-Platform**: Supports Terraform, CloudFormation, Kubernetes, Dockerfile, and more
- **Policy-as-Code**: Uses both built-in and custom policies
- **Graph-Based Analysis**: Understands resource relationships and dependencies

## 🌟 Key Features and Benefits

### 1. **Comprehensive Coverage**
```
✅ 1000+ built-in policies
✅ CIS Benchmarks compliance
✅ NIST, SOC2, HIPAA frameworks
✅ Custom policy creation
```

### 2. **Multi-Format Support**
- **Terraform** (.tf files)
- **CloudFormation** (.yaml/.json)
- **Kubernetes** (.yaml)
- **Dockerfile**
- **ARM Templates**
- **Serverless Framework**

### 3. **Integration Capabilities**
- CI/CD pipelines (GitHub Actions, GitLab CI, Jenkins)
- IDE extensions (VSCode, IntelliJ)
- Cloud platforms (AWS, Azure, GCP)
- Container registries

### 4. **Flexible Output Formats**
- CLI (human-readable)
- JSON (machine-readable)
- JUnit XML (test reporting)
- SARIF (security analysis)
- HTML reports

## 🔄 When to Use Checkov in Terraform Lifecycle

```mermaid
graph LR
    A[Write Terraform] --> B[Pre-commit Hook]
    B --> C[Local Development]
    C --> D[Pull Request]
    D --> E[CI/CD Pipeline]
    E --> F[Deploy to Cloud]
    
    B -.-> G[Checkov Scan]
    D -.-> H[Checkov PR Check]
    E -.-> I[Checkov Gate]
    
    style G fill:#e1f5fe
    style H fill:#e1f5fe
    style I fill:#e1f5fe
```

### Integration Points:

1. **Pre-commit Hooks** - Catch issues before code is committed
2. **Local Development** - IDE integration for real-time feedback
3. **Pull Request Checks** - Automated scanning on PR creation
4. **CI/CD Gates** - Block deployments with security issues
5. **Scheduled Scans** - Regular compliance checks on existing infrastructure

## 🛠️ Supported IaC Tools

| Tool | Support Level | File Types | Notes |
|------|---------------|------------|-------|
| **Terraform** | ⭐⭐⭐⭐⭐ | `.tf`, `.tfvars` | Full graph analysis |
| **CloudFormation** | ⭐⭐⭐⭐⭐ | `.yaml`, `.json` | Template validation |
| **Kubernetes** | ⭐⭐⭐⭐ | `.yaml`, `.yml` | Manifest scanning |
| **Dockerfile** | ⭐⭐⭐⭐ | `Dockerfile` | Image security |
| **Helm** | ⭐⭐⭐ | Charts | Template rendering |
| **Kustomize** | ⭐⭐⭐ | `.yaml` | Overlay support |

## 📦 Installation Methods

### Method 1: pip (Recommended)
```bash
# Install latest version
pip install checkov

# Install specific version
pip install checkov==2.4.0

# Verify installation
checkov --version
```

### Method 2: Docker
```bash
# Pull latest image
docker pull bridgecrew/checkov

# Run scan with Docker
docker run --rm -v $(pwd):/tf bridgecrew/checkov -d /tf
```

### Method 3: Homebrew (macOS)
```bash
# Install via Homebrew
brew install checkov

# Verify installation
checkov --version
```

### Method 4: VSCode Extension
1. Open VSCode
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Checkov"
4. Install "Checkov" by Bridgecrew

### Method 5: Binary Download
```bash
# Download binary (Linux/macOS)
curl -L https://github.com/bridgecrewio/checkov/releases/latest/download/checkov-linux -o checkov
chmod +x checkov
sudo mv checkov /usr/local/bin/
```

## 🔧 Quick Verification

Create a simple test to verify your installation:

```bash
# Create test directory
mkdir checkov-test && cd checkov-test

# Create simple Terraform file
cat > main.tf << EOF
resource "aws_s3_bucket" "example" {
  bucket = "my-test-bucket"
}
EOF

# Run Checkov scan
checkov -f main.tf
```

Expected output should show security findings for the S3 bucket configuration.

## 📊 Understanding Checkov Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   IaC Files     │───▶│   Checkov Core   │───▶│   Reports       │
│                 │    │                  │    │                 │
│ • Terraform     │    │ • Parser         │    │ • CLI Output    │
│ • CloudFormation│    │ • Graph Builder  │    │ • JSON/XML      │
│ • Kubernetes    │    │ • Policy Engine  │    │ • SARIF         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Policy Store   │
                       │                  │
                       │ • Built-in       │
                       │ • Custom YAML    │
                       │ • Custom Python  │
                       └──────────────────┘
```

## 🎯 Next Steps

Now that you understand what Checkov is and have it installed, you're ready to:

1. **Run your first scan** → [02 - First Scan and CLI Basics](../02_first_scan_and_cli_basics/)
2. **Explore the CLI options** and understand output formats
3. **Learn about different check types** and severity levels

## 📚 Additional Resources

- [Official Checkov Documentation](https://www.checkov.io/1.Welcome/Quick%20Start.html)
- [Checkov GitHub Repository](https://github.com/bridgecrewio/checkov)
- [Bridgecrew Community](https://bridgecrew.io/community/)
- [AWS Security Best Practices](https://aws.amazon.com/architecture/security-identity-compliance/)

---

**🎉 Congratulations!** You've completed the introduction to Checkov. You now understand what it is, why it's important, and have it installed and ready to use.

**Next:** [02 - First Scan and CLI Basics](../02_first_scan_and_cli_basics/) →
