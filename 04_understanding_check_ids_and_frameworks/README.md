# 04 - Understanding Check IDs and Frameworks

## 🎯 Learning Objectives
- Understand <PERSON><PERSON>'s check ID naming conventions and structure
- Map check IDs to specific security policies and compliance frameworks
- Explore AWS-specific checks and their categories
- Learn about compliance frameworks (CIS, NIST, SOC2, HIPAA)
- Filter and organize checks based on frameworks and categories

## 🏷️ Check ID Naming Convention

Check<PERSON> uses a structured naming convention for check IDs:

```
CKV_[PROVIDER]_[NUMBER]
```

### Examples:
- `CKV_AWS_18` - AWS S3 bucket access logging
- `CKV_AZURE_35` - Azure storage account encryption
- `CKV_GCP_6` - GCP compute instance disk encryption
- `CKV_K8S_8` - Kubernetes liveness probe
- `CKV_DOCKER_2` - Docker user directive

### Special Prefixes:
- `CKV2_AWS_*` - Graph-based checks (cross-resource analysis)
- `CKV_TF_*` - Terraform-specific checks
- `BC_AWS_*` - Bridgecrew Cloud specific checks

## 📊 AWS Check Categories

### 1. **Identity and Access Management (IAM)**
| Check ID | Description | Severity |
|----------|-------------|----------|
| CKV_AWS_1 | Ensure IAM policy does not grant '*' permissions | HIGH |
| CKV_AWS_39 | Ensure IAM password policy requires minimum length | MEDIUM |
| CKV_AWS_40 | Ensure IAM password policy requires uppercase | MEDIUM |
| CKV_AWS_41 | Ensure IAM password policy requires lowercase | MEDIUM |
| CKV_AWS_42 | Ensure IAM password policy requires symbols | MEDIUM |
| CKV_AWS_43 | Ensure IAM password policy requires numbers | MEDIUM |

### 2. **Storage (S3)**
| Check ID | Description | Severity |
|----------|-------------|----------|
| CKV_AWS_18 | Ensure S3 bucket has access logging configured | MEDIUM |
| CKV_AWS_19 | Ensure S3 bucket has server-side encryption | HIGH |
| CKV_AWS_20 | Ensure S3 bucket has MFA delete enabled | MEDIUM |
| CKV_AWS_21 | Ensure S3 bucket has versioning enabled | MEDIUM |
| CKV_AWS_54 | Ensure S3 bucket has block public ACLs enabled | HIGH |
| CKV_AWS_55 | Ensure S3 bucket has block public policy enabled | HIGH |

### 3. **Compute (EC2)**
| Check ID | Description | Severity |
|----------|-------------|----------|
| CKV_AWS_8 | Ensure EBS volume is encrypted | HIGH |
| CKV_AWS_79 | Ensure EC2 instance has security group attached | MEDIUM |
| CKV_AWS_126 | Ensure EC2 instance detailed monitoring enabled | LOW |
| CKV_AWS_135 | Ensure EC2 instance has IMDSv2 enabled | MEDIUM |

### 4. **Database (RDS)**
| Check ID | Description | Severity |
|----------|-------------|----------|
| CKV_AWS_16 | Ensure RDS database has encryption enabled | HIGH |
| CKV_AWS_17 | Ensure RDS database has backup enabled | MEDIUM |
| CKV_AWS_118 | Ensure RDS instance has enhanced monitoring | LOW |
| CKV_AWS_129 | Ensure RDS cluster has backup retention | MEDIUM |

### 5. **Networking (VPC, Security Groups)**
| Check ID | Description | Severity |
|----------|-------------|----------|
| CKV_AWS_23 | Ensure security group does not allow 0.0.0.0/0 ingress | HIGH |
| CKV_AWS_24 | Ensure security group does not allow unrestricted SSH | CRITICAL |
| CKV_AWS_25 | Ensure security group does not allow unrestricted RDP | CRITICAL |
| CKV_AWS_60 | Ensure VPC has flow logging enabled | MEDIUM |

## 🏛️ Compliance Frameworks

### 1. **CIS (Center for Internet Security) Benchmarks**

CIS provides security configuration benchmarks for various platforms:

```bash
# Filter by CIS compliance
checkov -d . --framework terraform --check CIS_AWS

# Specific CIS checks
checkov -d . --check CKV_AWS_1,CKV_AWS_16,CKV_AWS_18
```

**Key CIS AWS Checks:**
- **CIS 1.1**: Avoid root account usage
- **CIS 2.1**: CloudTrail enabled in all regions
- **CIS 2.3**: CloudTrail log file validation enabled
- **CIS 3.1**: VPC Flow Logs enabled

### 2. **NIST (National Institute of Standards and Technology)**

NIST Cybersecurity Framework mapping:

```bash
# NIST compliance filtering
checkov -d . --framework terraform --compliance-framework nist
```

**NIST Categories:**
- **Identify (ID)**: Asset management, governance
- **Protect (PR)**: Access control, data security
- **Detect (DE)**: Monitoring, detection processes
- **Respond (RS)**: Response planning, communications
- **Recover (RC)**: Recovery planning, improvements

### 3. **SOC 2 (Service Organization Control 2)**

SOC 2 Trust Service Criteria:

```bash
# SOC 2 compliance checks
checkov -d . --framework terraform --compliance-framework soc2
```

**SOC 2 Principles:**
- **Security**: Protection against unauthorized access
- **Availability**: System availability for operation
- **Processing Integrity**: Complete, valid, accurate processing
- **Confidentiality**: Information designated as confidential
- **Privacy**: Personal information collection and processing

### 4. **HIPAA (Health Insurance Portability and Accountability Act)**

Healthcare data protection requirements:

```bash
# HIPAA compliance filtering
checkov -d . --framework terraform --compliance-framework hipaa
```

**Key HIPAA Requirements:**
- Data encryption at rest and in transit
- Access controls and audit logging
- Backup and disaster recovery
- Network security controls

## 🔍 Exploring Check Details

### Getting Check Information

```bash
# List all available checks
checkov --list

# List AWS-specific checks
checkov --list | grep CKV_AWS

# Get detailed information about a specific check
checkov --check CKV_AWS_18 --list

# Show check documentation
checkov --check CKV_AWS_18 --guide
```

### Check Metadata Structure

Each check contains:
```json
{
  "check_id": "CKV_AWS_18",
  "check_name": "Ensure the S3 bucket has access logging configured",
  "check_result": {
    "result": "FAILED",
    "evaluated_keys": ["logging"]
  },
  "code_block": "...",
  "file_path": "/path/to/file.tf",
  "file_line_range": [15, 17],
  "resource": "aws_s3_bucket.example",
  "evaluations": null,
  "check_class": "checkov.terraform.checks.resource.aws.S3AccessLogs",
  "fixed_definition": null,
  "entity_tags": {
    "checkov": ["CKV_AWS_18"]
  },
  "caller_file_path": null,
  "caller_file_line_range": null,
  "resource_address": null,
  "severity": "MEDIUM",
  "bc_category": "logging",
  "benchmarks": {
    "CIS": ["2.3"],
    "NIST": ["PR.PT-1"],
    "SOC2": ["CC6.1"]
  }
}
```

## 🎯 Filtering by Frameworks and Categories

### 1. Framework-Based Filtering

```bash
# Terraform framework only
checkov -d . --framework terraform

# Multiple frameworks
checkov -d . --framework terraform,kubernetes

# Exclude specific frameworks
checkov -d . --skip-framework dockerfile
```

### 2. Category-Based Filtering

```bash
# Security categories
checkov -d . --category encryption
checkov -d . --category logging,monitoring
checkov -d . --category networking,iam

# Available categories:
# - encryption
# - logging
# - monitoring  
# - networking
# - iam
# - backup
# - general
```

### 3. Severity-Based Filtering

```bash
# High and critical only
checkov -d . --severity HIGH,CRITICAL

# Exclude low severity
checkov -d . --severity MEDIUM,HIGH,CRITICAL
```

### 4. Compliance Framework Filtering

```bash
# CIS benchmarks
checkov -d . --framework terraform --benchmark CIS_AWS

# Multiple compliance frameworks
checkov -d . --benchmark CIS_AWS,NIST_800_53

# Custom compliance filtering
checkov -d . --check CKV_AWS_1,CKV_AWS_16,CKV_AWS_18 --compact
```

## 📋 Practical Examples

### Example 1: CIS AWS Benchmark Scan
```bash
# Create a comprehensive CIS compliance report
checkov -d . \
  --framework terraform \
  --benchmark CIS_AWS \
  --output json \
  --output-file-path cis-compliance-report.json
```

### Example 2: High-Severity Security Issues
```bash
# Focus on critical security issues
checkov -d . \
  --severity CRITICAL,HIGH \
  --category encryption,iam,networking \
  --compact
```

### Example 3: Database Security Audit
```bash
# Database-specific security checks
checkov -d . \
  --check CKV_AWS_16,CKV_AWS_17,CKV_AWS_118,CKV_AWS_129 \
  --output html \
  --output-file-path database-security-report.html
```

## 🔧 Custom Check ID Management

### Creating Check ID Mappings

Create a mapping file for your organization:

```yaml
# check-mappings.yml
compliance_mappings:
  internal_security_policy:
    encryption_required:
      - CKV_AWS_16  # RDS encryption
      - CKV_AWS_19  # S3 encryption
      - CKV_AWS_8   # EBS encryption
    
    logging_required:
      - CKV_AWS_18  # S3 access logging
      - CKV_AWS_60  # VPC flow logs
      - CKV_AWS_76  # CloudTrail logging
    
    network_security:
      - CKV_AWS_23  # Security group restrictions
      - CKV_AWS_24  # SSH restrictions
      - CKV_AWS_25  # RDP restrictions

  regulatory_compliance:
    hipaa:
      - CKV_AWS_16  # Data encryption
      - CKV_AWS_18  # Access logging
      - CKV_AWS_39  # Password policy
    
    sox:
      - CKV_AWS_76  # Audit logging
      - CKV_AWS_1   # IAM permissions
      - CKV_AWS_17  # Database backups
```

### Using Custom Mappings

```bash
# Load custom check configuration
checkov -d . --config-file check-mappings.yml

# Run specific compliance set
checkov -d . --check $(cat compliance-checks.txt | tr '\n' ',')
```

## 📊 Check Statistics and Analysis

### Generating Check Statistics

```bash
# Count checks by category
checkov -d . --output json | jq '.summary.by_category'

# Count checks by severity
checkov -d . --output json | jq '.summary.by_severity'

# Failed checks by framework
checkov -d . --output json | jq '.results.failed_checks[] | .check_id' | sort | uniq -c
```

### Analysis Scripts

Create analysis scripts for check patterns:

```python
#!/usr/bin/env python3
# analyze_checks.py
import json
import sys
from collections import defaultdict

def analyze_checkov_results(json_file):
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    # Analyze by category
    category_stats = defaultdict(int)
    severity_stats = defaultdict(int)
    
    for check in data.get('results', {}).get('failed_checks', []):
        category_stats[check.get('bc_category', 'unknown')] += 1
        severity_stats[check.get('severity', 'unknown')] += 1
    
    print("Failed Checks by Category:")
    for category, count in sorted(category_stats.items()):
        print(f"  {category}: {count}")
    
    print("\nFailed Checks by Severity:")
    for severity, count in sorted(severity_stats.items()):
        print(f"  {severity}: {count}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 analyze_checks.py <checkov_results.json>")
        sys.exit(1)
    
    analyze_checkov_results(sys.argv[1])
```

## 🎯 Next Steps

You now understand:
- ✅ Check ID naming conventions and structure
- ✅ AWS-specific check categories and their importance
- ✅ Major compliance frameworks and their mappings
- ✅ How to filter and organize checks effectively

**Next:** [05 - Custom Policies with YAML](../05_custom_policies_with_yaml/) →

## 📚 Quick Reference

### Essential Commands
```bash
# List all checks
checkov --list

# Framework-specific checks
checkov -d . --framework terraform

# Compliance filtering
checkov -d . --benchmark CIS_AWS

# Category filtering
checkov -d . --category encryption,logging

# Severity filtering
checkov -d . --severity HIGH,CRITICAL
```

### Key AWS Check Categories
- **IAM**: CKV_AWS_1, CKV_AWS_39-43
- **S3**: CKV_AWS_18-21, CKV_AWS_54-55
- **EC2**: CKV_AWS_8, CKV_AWS_79, CKV_AWS_135
- **RDS**: CKV_AWS_16-17, CKV_AWS_118, CKV_AWS_129
- **Network**: CKV_AWS_23-25, CKV_AWS_60

---

**🎉 Excellent!** You now have a deep understanding of Checkov's check system and can effectively navigate and filter the extensive library of security checks.
