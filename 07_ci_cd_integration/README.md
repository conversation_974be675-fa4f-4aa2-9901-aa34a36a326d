# 07 - CI/CD Integration

## 🎯 Learning Objectives
- Integrate <PERSON><PERSON> into GitHub Actions workflows
- Set up GitLab CI and Jenkins pipelines with Checkov
- Configure failure conditions and security gates
- Implement PR/MR comment automation
- Create comprehensive security scanning workflows

## 🔄 Why CI/CD Integration?

Integrating Checkov into CI/CD pipelines provides:
- **Shift-Left Security**: Catch issues before deployment
- **Automated Enforcement**: Consistent security checks
- **Fast Feedback**: Immediate results on code changes
- **Compliance Automation**: Continuous compliance validation
- **Team Collaboration**: Shared security standards

## 🚀 GitHub Actions Integration

### Basic GitHub Actions Workflow

```yaml
# .github/workflows/checkov.yml
name: Checkov Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  checkov-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Run Checkov action
      id: checkov
      uses: bridgecrewio/checkov-action@master
      with:
        directory: .
        framework: terraform
        output_format: sarif
        output_file_path: reports/results.sarif
        
    - name: Upload SARIF to GitHub
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: reports/results.sarif
```

### Advanced GitHub Actions with Custom Policies

```yaml
# .github/workflows/advanced-checkov.yml
name: Advanced Checkov Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  CHECKOV_VERSION: "2.4.0"

jobs:
  checkov-scan:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      pull-requests: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Full history for better analysis
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install Checkov
      run: |
        pip install checkov==${{ env.CHECKOV_VERSION }}
        checkov --version
    
    - name: Create results directory
      run: mkdir -p reports
    
    - name: Run Checkov with custom policies
      id: checkov
      run: |
        checkov -d . \
          --framework terraform \
          --external-checks-dir ./custom-policies/ \
          --output json \
          --output-file-path reports/checkov-results.json \
          --output sarif \
          --output-file-path reports/checkov-results.sarif \
          --soft-fail
      continue-on-error: true
    
    - name: Parse Checkov results
      id: parse-results
      run: |
        if [ -f reports/checkov-results.json ]; then
          FAILED_CHECKS=$(jq '.results.failed_checks | length' reports/checkov-results.json)
          PASSED_CHECKS=$(jq '.results.passed_checks | length' reports/checkov-results.json)
          
          echo "failed_checks=$FAILED_CHECKS" >> $GITHUB_OUTPUT
          echo "passed_checks=$PASSED_CHECKS" >> $GITHUB_OUTPUT
          
          if [ "$FAILED_CHECKS" -gt 0 ]; then
            echo "checkov_status=failed" >> $GITHUB_OUTPUT
          else
            echo "checkov_status=passed" >> $GITHUB_OUTPUT
          fi
        else
          echo "checkov_status=error" >> $GITHUB_OUTPUT
        fi
    
    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const failedChecks = '${{ steps.parse-results.outputs.failed_checks }}';
          const passedChecks = '${{ steps.parse-results.outputs.passed_checks }}';
          const status = '${{ steps.parse-results.outputs.checkov_status }}';
          
          let emoji = status === 'passed' ? '✅' : '❌';
          let statusText = status === 'passed' ? 'PASSED' : 'FAILED';
          
          const comment = `## ${emoji} Checkov Security Scan ${statusText}
          
          **Results Summary:**
          - ✅ Passed checks: ${passedChecks}
          - ❌ Failed checks: ${failedChecks}
          
          ${failedChecks > 0 ? '⚠️ Please review and fix the security issues before merging.' : '🎉 All security checks passed!'}
          
          <details>
          <summary>View detailed results</summary>
          
          Check the [Security tab](${context.payload.repository.html_url}/security/code-scanning) for detailed findings.
          </details>`;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
    
    - name: Upload SARIF to GitHub Security
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: reports/checkov-results.sarif
    
    - name: Upload results as artifact
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: checkov-results
        path: reports/
    
    - name: Fail job if security issues found
      if: steps.parse-results.outputs.checkov_status == 'failed'
      run: |
        echo "❌ Security issues found. Please review and fix before merging."
        exit 1
```

### Matrix Strategy for Multiple Environments

```yaml
# .github/workflows/multi-env-checkov.yml
name: Multi-Environment Security Scan

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  checkov-scan:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [dev, staging, prod]
        include:
          - environment: dev
            severity: MEDIUM,HIGH,CRITICAL
            config: .checkov-dev.yml
          - environment: staging
            severity: HIGH,CRITICAL
            config: .checkov-staging.yml
          - environment: prod
            severity: CRITICAL
            config: .checkov-prod.yml
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Run Checkov for ${{ matrix.environment }}
      uses: bridgecrewio/checkov-action@master
      with:
        directory: environments/${{ matrix.environment }}
        framework: terraform
        config_file: ${{ matrix.config }}
        soft_fail: false
        output_format: json
        output_file_path: results-${{ matrix.environment }}.json
```

## 🦊 GitLab CI Integration

### Basic GitLab CI Configuration

```yaml
# .gitlab-ci.yml
stages:
  - security
  - deploy

variables:
  CHECKOV_VERSION: "2.4.0"

checkov-scan:
  stage: security
  image: python:3.9-slim
  
  before_script:
    - pip install checkov==$CHECKOV_VERSION
    - mkdir -p reports
  
  script:
    - |
      checkov -d . \
        --framework terraform \
        --output json \
        --output-file-path reports/checkov-results.json \
        --output junit \
        --output-file-path reports/checkov-junit.xml \
        --soft-fail
  
  artifacts:
    reports:
      junit: reports/checkov-junit.xml
    paths:
      - reports/
    expire_in: 1 week
  
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
```

### Advanced GitLab CI with Security Dashboard

```yaml
# .gitlab-ci.yml
include:
  - template: Security/SAST.gitlab-ci.yml

stages:
  - security
  - deploy

variables:
  CHECKOV_VERSION: "2.4.0"
  SECURE_LOG_LEVEL: "debug"

.checkov-base:
  stage: security
  image: python:3.9-slim
  before_script:
    - apt-get update && apt-get install -y git jq
    - pip install checkov==$CHECKOV_VERSION
    - mkdir -p reports
  artifacts:
    reports:
      sast: reports/gl-sast-report.json
      junit: reports/checkov-junit.xml
    paths:
      - reports/
    expire_in: 1 week

checkov-terraform:
  extends: .checkov-base
  script:
    - |
      # Run Checkov scan
      checkov -d . \
        --framework terraform \
        --external-checks-dir ./custom-policies/ \
        --output json \
        --output-file-path reports/checkov-results.json \
        --output junit \
        --output-file-path reports/checkov-junit.xml \
        --output sarif \
        --output-file-path reports/checkov-results.sarif \
        --soft-fail
      
      # Convert SARIF to GitLab SAST format
      python3 scripts/sarif-to-gitlab.py \
        reports/checkov-results.sarif \
        reports/gl-sast-report.json
  
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

checkov-merge-request-comment:
  stage: security
  image: python:3.9-slim
  dependencies:
    - checkov-terraform
  before_script:
    - pip install requests jq
  script:
    - |
      if [ -f reports/checkov-results.json ]; then
        FAILED_CHECKS=$(jq '.results.failed_checks | length' reports/checkov-results.json)
        PASSED_CHECKS=$(jq '.results.passed_checks | length' reports/checkov-results.json)
        
        # Create MR comment
        python3 scripts/create-mr-comment.py \
          --failed-checks $FAILED_CHECKS \
          --passed-checks $PASSED_CHECKS \
          --project-id $CI_PROJECT_ID \
          --merge-request-iid $CI_MERGE_REQUEST_IID \
          --gitlab-token $GITLAB_TOKEN
      fi
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  when: always
```

## 🔨 Jenkins Integration

### Jenkins Pipeline (Declarative)

```groovy
// Jenkinsfile
pipeline {
    agent any
    
    environment {
        CHECKOV_VERSION = '2.4.0'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Setup') {
            steps {
                sh '''
                    python3 -m pip install --user checkov==${CHECKOV_VERSION}
                    mkdir -p reports
                '''
            }
        }
        
        stage('Checkov Security Scan') {
            steps {
                script {
                    def checkovExitCode = sh(
                        script: '''
                            ~/.local/bin/checkov -d . \
                                --framework terraform \
                                --external-checks-dir ./custom-policies/ \
                                --output json \
                                --output-file-path reports/checkov-results.json \
                                --output junit \
                                --output-file-path reports/checkov-junit.xml \
                                --soft-fail
                        ''',
                        returnStatus: true
                    )
                    
                    // Parse results
                    def results = readJSON file: 'reports/checkov-results.json'
                    def failedChecks = results.results.failed_checks.size()
                    def passedChecks = results.results.passed_checks.size()
                    
                    // Set build description
                    currentBuild.description = "Passed: ${passedChecks}, Failed: ${failedChecks}"
                    
                    // Fail build if critical issues found
                    if (failedChecks > 0) {
                        currentBuild.result = 'UNSTABLE'
                        echo "⚠️ Security issues found: ${failedChecks} failed checks"
                    }
                }
            }
            post {
                always {
                    // Publish test results
                    publishTestResults testResultsPattern: 'reports/checkov-junit.xml'
                    
                    // Archive artifacts
                    archiveArtifacts artifacts: 'reports/**/*', fingerprint: true
                }
            }
        }
        
        stage('Security Gate') {
            when {
                expression {
                    def results = readJSON file: 'reports/checkov-results.json'
                    def criticalIssues = results.results.failed_checks.findAll { 
                        it.severity == 'CRITICAL' 
                    }
                    return criticalIssues.size() > 0
                }
            }
            steps {
                error('Critical security issues found. Deployment blocked.')
            }
        }
    }
    
    post {
        always {
            // Clean workspace
            cleanWs()
        }
        failure {
            // Send notification
            emailext (
                subject: "Security Scan Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "Security scan failed. Please check the build logs for details.",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
```

### Jenkins Shared Library

```groovy
// vars/checkovScan.groovy
def call(Map config) {
    def directory = config.directory ?: '.'
    def framework = config.framework ?: 'terraform'
    def customPolicies = config.customPolicies ?: ''
    def failOnCritical = config.failOnCritical ?: true
    def outputFormat = config.outputFormat ?: 'json'
    
    pipeline {
        agent any
        
        stages {
            stage('Checkov Security Scan') {
                steps {
                    script {
                        // Build Checkov command
                        def checkovCmd = "checkov -d ${directory} --framework ${framework}"
                        
                        if (customPolicies) {
                            checkovCmd += " --external-checks-dir ${customPolicies}"
                        }
                        
                        checkovCmd += " --output ${outputFormat} --output-file-path reports/results.${outputFormat}"
                        checkovCmd += " --soft-fail"
                        
                        // Run scan
                        sh "mkdir -p reports"
                        def exitCode = sh(script: checkovCmd, returnStatus: true)
                        
                        // Process results
                        if (outputFormat == 'json') {
                            def results = readJSON file: "reports/results.json"
                            def failedChecks = results.results.failed_checks
                            def criticalIssues = failedChecks.findAll { it.severity == 'CRITICAL' }
                            
                            if (failOnCritical && criticalIssues.size() > 0) {
                                error("Critical security issues found: ${criticalIssues.size()}")
                            }
                        }
                    }
                }
            }
        }
    }
}
```

## 🔧 Configuration Management

### Environment-Specific Configurations

```yaml
# .checkov-dev.yml
framework:
  - terraform

severity:
  - MEDIUM
  - HIGH
  - CRITICAL

external-checks-dir:
  - ./custom-policies/

skip-check:
  - CKV_AWS_18  # S3 logging not required in dev

output: json
```

```yaml
# .checkov-prod.yml
framework:
  - terraform

severity:
  - CRITICAL

external-checks-dir:
  - ./custom-policies/

# No skipped checks in production
soft-fail: false
output: sarif
```

### Baseline Management

```bash
# Generate baseline for existing infrastructure
checkov -d . --create-baseline

# Use baseline in CI/CD
checkov -d . --baseline .checkov.baseline
```

## 📊 Reporting and Notifications

### Slack Notifications

```python
# scripts/slack-notify.py
import json
import requests
import sys

def send_slack_notification(webhook_url, results_file):
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    failed_checks = len(results['results']['failed_checks'])
    passed_checks = len(results['results']['passed_checks'])
    
    color = "good" if failed_checks == 0 else "danger"
    
    message = {
        "attachments": [
            {
                "color": color,
                "title": "Checkov Security Scan Results",
                "fields": [
                    {
                        "title": "Passed Checks",
                        "value": str(passed_checks),
                        "short": True
                    },
                    {
                        "title": "Failed Checks", 
                        "value": str(failed_checks),
                        "short": True
                    }
                ]
            }
        ]
    }
    
    response = requests.post(webhook_url, json=message)
    return response.status_code == 200

if __name__ == "__main__":
    webhook_url = sys.argv[1]
    results_file = sys.argv[2]
    send_slack_notification(webhook_url, results_file)
```

### Email Reports

```python
# scripts/email-report.py
import json
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import sys

def generate_html_report(results):
    failed_checks = results['results']['failed_checks']
    passed_checks = results['results']['passed_checks']
    
    html = f"""
    <html>
    <body>
        <h2>Checkov Security Scan Report</h2>
        <p><strong>Summary:</strong></p>
        <ul>
            <li>Passed Checks: {len(passed_checks)}</li>
            <li>Failed Checks: {len(failed_checks)}</li>
        </ul>
        
        <h3>Failed Checks:</h3>
        <table border="1">
            <tr>
                <th>Check ID</th>
                <th>Resource</th>
                <th>File</th>
                <th>Severity</th>
            </tr>
    """
    
    for check in failed_checks:
        html += f"""
            <tr>
                <td>{check['check_id']}</td>
                <td>{check['resource']}</td>
                <td>{check['file_path']}</td>
                <td>{check.get('severity', 'UNKNOWN')}</td>
            </tr>
        """
    
    html += """
        </table>
    </body>
    </html>
    """
    
    return html

def send_email_report(smtp_server, smtp_port, username, password, 
                     from_email, to_emails, results_file):
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    msg = MIMEMultipart('alternative')
    msg['Subject'] = "Checkov Security Scan Report"
    msg['From'] = from_email
    msg['To'] = ", ".join(to_emails)
    
    html_content = generate_html_report(results)
    html_part = MIMEText(html_content, 'html')
    msg.attach(html_part)
    
    server = smtplib.SMTP(smtp_server, smtp_port)
    server.starttls()
    server.login(username, password)
    server.send_message(msg)
    server.quit()

if __name__ == "__main__":
    # Usage: python email-report.py results.json
    results_file = sys.argv[1]
    # Configure your SMTP settings
    send_email_report(
        smtp_server="smtp.gmail.com",
        smtp_port=587,
        username="<EMAIL>",
        password="your-app-password",
        from_email="<EMAIL>",
        to_emails=["<EMAIL>"],
        results_file=results_file
    )
```

## 🎯 Next Steps

You now know how to:
- ✅ Integrate Checkov into various CI/CD platforms
- ✅ Configure security gates and failure conditions
- ✅ Automate PR/MR comments and notifications
- ✅ Manage environment-specific configurations

**Next:** [08 - Excluding and Skipping Checks](../08_excluding_and_skipping_checks/) →

## 📚 Quick Reference

### GitHub Actions Basic Template
```yaml
- name: Run Checkov
  uses: bridgecrewio/checkov-action@master
  with:
    directory: .
    framework: terraform
    output_format: sarif
```

### GitLab CI Basic Template
```yaml
checkov:
  script:
    - pip install checkov
    - checkov -d . --framework terraform
```

### Jenkins Basic Template
```groovy
sh 'checkov -d . --framework terraform --output json'
```

---

**🎉 Excellent!** You've mastered CI/CD integration with Checkov. Your infrastructure security is now automated and continuously monitored across your development workflow.
