// Jenkins Pipeline for Checkov Security Scanning
// Place this file as 'Jenkins<PERSON><PERSON>' in your repository root

pipeline {
    agent any
    
    environment {
        CHECKOV_VERSION = '2.4.0'
        PYTHON_PATH = '/usr/bin/python3'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
                echo "Checked out code from ${env.GIT_BRANCH}"
            }
        }
        
        stage('Setup Environment') {
            steps {
                sh '''
                    echo "Setting up Python environment..."
                    python3 --version
                    python3 -m pip install --user checkov==${CHECKOV_VERSION}
                    mkdir -p reports
                    echo "Checkov version:"
                    ~/.local/bin/checkov --version
                '''
            }
        }
        
        stage('Checkov Security Scan') {
            steps {
                script {
                    echo "Running Checkov security scan..."
                    
                    def checkovExitCode = sh(
                        script: '''
                            ~/.local/bin/checkov -d . \
                                --framework terraform \
                                --external-checks-dir ./custom-policies/ \
                                --output json \
                                --output-file-path reports/checkov-results.json \
                                --output junit \
                                --output-file-path reports/checkov-junit.xml \
                                --soft-fail
                        ''',
                        returnStatus: true
                    )
                    
                    echo "Checkov scan completed with exit code: ${checkovExitCode}"
                    
                    // Parse results if JSON file exists
                    if (fileExists('reports/checkov-results.json')) {
                        def results = readJSON file: 'reports/checkov-results.json'
                        def failedChecks = results.results.failed_checks.size()
                        def passedChecks = results.results.passed_checks.size()
                        def criticalIssues = results.results.failed_checks.findAll { 
                            it.severity == 'CRITICAL' 
                        }.size()
                        
                        // Set build description
                        currentBuild.description = "✅ Passed: ${passedChecks} | ❌ Failed: ${failedChecks} | 🚨 Critical: ${criticalIssues}"
                        
                        // Store results as environment variables for later stages
                        env.CHECKOV_FAILED_CHECKS = failedChecks.toString()
                        env.CHECKOV_PASSED_CHECKS = passedChecks.toString()
                        env.CHECKOV_CRITICAL_ISSUES = criticalIssues.toString()
                        
                        // Log summary
                        echo "Checkov Results Summary:"
                        echo "  Passed Checks: ${passedChecks}"
                        echo "  Failed Checks: ${failedChecks}"
                        echo "  Critical Issues: ${criticalIssues}"
                        
                        // Set build status based on results
                        if (criticalIssues > 0) {
                            currentBuild.result = 'FAILURE'
                            error("🚨 Critical security issues found: ${criticalIssues}")
                        } else if (failedChecks > 0) {
                            currentBuild.result = 'UNSTABLE'
                            echo "⚠️ Security issues found: ${failedChecks} failed checks"
                        } else {
                            echo "🎉 All security checks passed!"
                        }
                    } else {
                        currentBuild.result = 'FAILURE'
                        error("Checkov results file not found")
                    }
                }
            }
            post {
                always {
                    // Publish test results
                    publishTestResults testResultsPattern: 'reports/checkov-junit.xml'
                    
                    // Archive artifacts
                    archiveArtifacts artifacts: 'reports/**/*', fingerprint: true, allowEmptyArchive: true
                }
            }
        }
        
        stage('Security Gate') {
            when {
                expression {
                    return env.CHECKOV_CRITICAL_ISSUES?.toInteger() > 0
                }
            }
            steps {
                echo "🛑 Security gate triggered due to critical issues"
                echo "Critical issues found: ${env.CHECKOV_CRITICAL_ISSUES}"
                error('Critical security issues detected. Deployment blocked.')
            }
        }
        
        stage('Deploy') {
            when {
                allOf {
                    branch 'main'
                    expression { currentBuild.result != 'FAILURE' }
                }
            }
            steps {
                echo "🚀 Deploying application..."
                echo "Security checks passed. Proceeding with deployment."
                // Add your deployment steps here
            }
        }
    }
    
    post {
        always {
            echo "Pipeline completed"
            
            // Generate and display summary
            script {
                if (env.CHECKOV_FAILED_CHECKS) {
                    def summary = """
                    📊 Checkov Security Scan Summary:
                    ✅ Passed Checks: ${env.CHECKOV_PASSED_CHECKS ?: '0'}
                    ❌ Failed Checks: ${env.CHECKOV_FAILED_CHECKS ?: '0'}
                    🚨 Critical Issues: ${env.CHECKOV_CRITICAL_ISSUES ?: '0'}
                    
                    Build Status: ${currentBuild.result ?: 'SUCCESS'}
                    """
                    echo summary
                }
            }
        }
        
        success {
            echo "✅ Pipeline completed successfully"
            // Add success notifications here
        }
        
        failure {
            echo "❌ Pipeline failed"
            
            // Send notification on failure
            script {
                if (env.CHANGE_AUTHOR_EMAIL) {
                    emailext (
                        subject: "🚨 Security Scan Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                        body: """
                        Security scan failed for ${env.JOB_NAME} build ${env.BUILD_NUMBER}.
                        
                        Critical Issues: ${env.CHECKOV_CRITICAL_ISSUES ?: '0'}
                        Failed Checks: ${env.CHECKOV_FAILED_CHECKS ?: '0'}
                        
                        Please check the build logs for details:
                        ${env.BUILD_URL}
                        
                        View the detailed security report in the build artifacts.
                        """,
                        to: "${env.CHANGE_AUTHOR_EMAIL}",
                        attachLog: true
                    )
                }
            }
        }
        
        unstable {
            echo "⚠️ Pipeline completed with warnings"
            // Add notification for unstable builds
        }
        
        cleanup {
            // Clean workspace
            cleanWs()
        }
    }
}
