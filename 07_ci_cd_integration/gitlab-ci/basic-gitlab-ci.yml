# Basic GitLab CI configuration for Checkov security scanning
# Add this to your .gitlab-ci.yml file

stages:
  - security
  - deploy

variables:
  CHECKOV_VERSION: "2.4.0"

checkov-scan:
  stage: security
  image: python:3.9-slim
  
  before_script:
    - pip install checkov==$CHECKOV_VERSION
    - mkdir -p reports
  
  script:
    - |
      checkov -d . \
        --framework terraform \
        --output json \
        --output-file-path reports/checkov-results.json \
        --output junit \
        --output-file-path reports/checkov-junit.xml \
        --soft-fail
  
  artifacts:
    reports:
      junit: reports/checkov-junit.xml
    paths:
      - reports/
    expire_in: 1 week
  
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
