# Advanced GitHub Actions workflow with custom policies and PR comments
# Place this file in .github/workflows/advanced-checkov.yml

name: Advanced Checkov Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  CHECKOV_VERSION: "2.4.0"

jobs:
  checkov-scan:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      pull-requests: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Full history for better analysis
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install Checkov
      run: |
        pip install checkov==${{ env.CHECKOV_VERSION }}
        checkov --version
    
    - name: Create results directory
      run: mkdir -p reports
    
    - name: Run Checkov with custom policies
      id: checkov
      run: |
        checkov -d . \
          --framework terraform \
          --external-checks-dir ./custom-policies/ \
          --output json \
          --output-file-path reports/checkov-results.json \
          --output sarif \
          --output-file-path reports/checkov-results.sarif \
          --soft-fail
      continue-on-error: true
    
    - name: Parse Checkov results
      id: parse-results
      run: |
        if [ -f reports/checkov-results.json ]; then
          FAILED_CHECKS=$(jq '.results.failed_checks | length' reports/checkov-results.json)
          PASSED_CHECKS=$(jq '.results.passed_checks | length' reports/checkov-results.json)
          CRITICAL_ISSUES=$(jq '.results.failed_checks | map(select(.severity == "CRITICAL")) | length' reports/checkov-results.json)
          
          echo "failed_checks=$FAILED_CHECKS" >> $GITHUB_OUTPUT
          echo "passed_checks=$PASSED_CHECKS" >> $GITHUB_OUTPUT
          echo "critical_issues=$CRITICAL_ISSUES" >> $GITHUB_OUTPUT
          
          if [ "$CRITICAL_ISSUES" -gt 0 ]; then
            echo "checkov_status=critical" >> $GITHUB_OUTPUT
          elif [ "$FAILED_CHECKS" -gt 0 ]; then
            echo "checkov_status=failed" >> $GITHUB_OUTPUT
          else
            echo "checkov_status=passed" >> $GITHUB_OUTPUT
          fi
        else
          echo "checkov_status=error" >> $GITHUB_OUTPUT
        fi
    
    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const failedChecks = '${{ steps.parse-results.outputs.failed_checks }}';
          const passedChecks = '${{ steps.parse-results.outputs.passed_checks }}';
          const criticalIssues = '${{ steps.parse-results.outputs.critical_issues }}';
          const status = '${{ steps.parse-results.outputs.checkov_status }}';
          
          let emoji = '✅';
          let statusText = 'PASSED';
          let alertLevel = '';
          
          if (status === 'critical') {
            emoji = '🚨';
            statusText = 'CRITICAL ISSUES FOUND';
            alertLevel = '> **⚠️ CRITICAL SECURITY ISSUES DETECTED - DEPLOYMENT BLOCKED**\n\n';
          } else if (status === 'failed') {
            emoji = '❌';
            statusText = 'ISSUES FOUND';
            alertLevel = '> **⚠️ Security issues detected. Please review before merging.**\n\n';
          }
          
          const comment = `## ${emoji} Checkov Security Scan ${statusText}
          
          ${alertLevel}**Results Summary:**
          - ✅ Passed checks: ${passedChecks}
          - ❌ Failed checks: ${failedChecks}
          - 🚨 Critical issues: ${criticalIssues}
          
          ${criticalIssues > 0 ? '🛑 **Critical security issues must be resolved before merging.**' : 
            failedChecks > 0 ? '⚠️ Please review and fix the security issues.' : 
            '🎉 All security checks passed!'}
          
          <details>
          <summary>📊 View detailed results</summary>
          
          Check the [Security tab](${context.payload.repository.html_url}/security/code-scanning) for detailed findings.
          
          You can also download the full report from the [workflow artifacts](${context.payload.repository.html_url}/actions/runs/${context.runId}).
          </details>`;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
    
    - name: Upload SARIF to GitHub Security
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: reports/checkov-results.sarif
    
    - name: Upload results as artifact
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: checkov-results
        path: reports/
        retention-days: 30
    
    - name: Fail job if critical security issues found
      if: steps.parse-results.outputs.checkov_status == 'critical'
      run: |
        echo "🚨 Critical security issues found. Deployment blocked."
        echo "Please review and fix the critical issues before proceeding."
        exit 1
