# Basic GitHub Actions workflow for Checkov security scanning
# Place this file in .github/workflows/checkov.yml

name: Checkov Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  checkov-scan:
    runs-on: ubuntu-latest
    name: Checkov Security Scan
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Run Checkov action
      id: checkov
      uses: bridgecrewio/checkov-action@master
      with:
        directory: .
        framework: terraform
        output_format: sarif
        output_file_path: reports/results.sarif
        soft_fail: false  # Set to true to not fail the build
        
    - name: Upload SARIF to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()  # Upload even if <PERSON><PERSON> fails
      with:
        sarif_file: reports/results.sarif
